STEP 1: SOPHISTICATED UNIT MANAGEMENT UPGRADE
==================================================

Copy this code and add to your working VBA module:


'==================================================================================
'  STEP 1: SOPHISTICATED UNIT MANAGEMENT UPGRADE
'==================================================================================

' Add this to your existing minimal system to get sophisticated unit management

Public Sub AddUnitToComplexAdvanced()
    ' Sophisticated unit management with validation and auto-numbering
    Dim complexWs As Worksheet, unitWs As Worksheet
    Set complexWs = ThisWorkbook.Sheets("Complexes")
    Set unitWs = ThisWorkbook.Sheets("Units")
    
    Dim chosenComplex As String, newUnitName As String, unitCount As Variant, prefix As String
    Dim i As Long, nextUnitRow As Long, lastUnitNum As Long, cell As Range, found As Boolean
    
    On Error GoTo ErrorHandler
    
    ' Step 1: Get and validate complex name
    chosenComplex = Application.InputBox("Enter the EXACT name of the complex these units belong to:", "Step 1: Assign Complex")
    If chosenComplex = "" Then Exit Sub
    
    ' Validate complex exists
    For Each cell In complexWs.Range("A2:A" & complexWs.Cells(complexWs.Rows.Count, "A").End(xlUp).Row)
        If UCase(cell.Value) = UCase(chosenComplex) Then found = True: Exit For
    Next cell
    If Not found Then
        MsgBox "The complex '" & chosenComplex & "' was not found. Please add it first using 'Manage Complexes'.", vbCritical, "Complex Not Found"
        Exit Sub
    End If
    
    ' Step 2: Get unit prefix
    prefix = Application.InputBox("Enter a prefix for the unit names (e.g., 'Unit', 'Flat', 'Suite').", "Step 2: Unit Name Prefix", "Unit")
    If prefix = "" Then Exit Sub
    
    ' Step 3: Get unit count
    unitCount = Application.InputBox("How many units do you want to create for this complex?", "Step 3: Number of Units", Type:=1)
    If unitCount = False Or Not IsNumeric(unitCount) Or unitCount < 1 Then Exit Sub
    
    Application.ScreenUpdating = False
    
    ' Step 4: Find last unit number for this complex (sophisticated duplicate prevention)
    lastUnitNum = 0
    For Each cell In unitWs.Range("A2:A" & unitWs.Cells(unitWs.Rows.Count, "A").End(xlUp).Row)
        If UCase(cell.Value) = UCase(chosenComplex) Then
            Dim currentUnitName As String
            currentUnitName = cell.Offset(0, 1).Value
            If UCase(Left(currentUnitName, Len(prefix))) = UCase(prefix) Then
                Dim numPart As String
                numPart = Trim(Mid(currentUnitName, Len(prefix) + 1))
                If IsNumeric(numPart) Then
                    If CLng(numPart) > lastUnitNum Then
                        lastUnitNum = CLng(numPart)
                    End If
                End If
            End If
        End If
    Next cell
    
    ' Step 5: Add units with auto-numbering
    nextUnitRow = unitWs.Cells(unitWs.Rows.Count, "A").End(xlUp).Row + 1
    
    For i = 1 To CLng(unitCount)
        newUnitName = prefix & " " & (lastUnitNum + i)
        unitWs.Cells(nextUnitRow, "A").Value = chosenComplex
        unitWs.Cells(nextUnitRow, "B").Value = newUnitName
        nextUnitRow = nextUnitRow + 1
    Next i
    
    Application.ScreenUpdating = True
    Call UpdateComplexNamedRange
    
    MsgBox unitCount & " units have been successfully added to the '" & chosenComplex & "' complex, starting from number " & lastUnitNum + 1 & ".", vbInformation, "Bulk Add Complete"
    Exit Sub
    
ErrorHandler:
    Application.ScreenUpdating = True
    MsgBox "Error in sophisticated unit management: " & Err.Description, vbCritical
End Sub

Public Sub UpdateComplexNamedRange()
    ' Create named range for dropdown validations
    Dim ws As Worksheet: Set ws = ThisWorkbook.Sheets("Complexes")
    Dim lastRow As Long: lastRow = ws.Cells(ws.Rows.Count, "A").End(xlUp).Row
    If lastRow < 2 Then lastRow = 2
    
    On Error Resume Next
    ThisWorkbook.Names("ComplexList").Delete
    On Error GoTo 0
    
    ThisWorkbook.Names.Add Name:="ComplexList", RefersTo:="='Complexes'!$A$2:$A$" & lastRow
End Sub

Public Sub TestStep1()
    MsgBox "✅ STEP 1 UPGRADE COMPLETE!" & vbCrLf & _
           "Sophisticated Unit Management Added:" & vbCrLf & _
           "• Complex validation" & vbCrLf & _
           "• Auto-numbering with duplicate prevention" & vbCrLf & _
           "• Bulk unit creation with prefixes" & vbCrLf & _
           "• Smart number continuation", vbInformation, "Step 1 Complete"
End Sub
