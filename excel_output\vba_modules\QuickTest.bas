'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
'~          QUICK TEST MODULE - VERIFY VBA COMPILATION AND EXECUTION
'~
'~ Description: Simple test functions to verify the Styled Card system works
'~              Run these tests to confirm successful installation
'~
'~ Version: V1.0
'~ Author: MiniMax Agent
'~
'~ INSTRUCTIONS: 
'~ 1. Import StyledCard_Core_Fixed.bas first
'~ 2. Import this module
'~ 3. Run QuickTest_Basic() to verify everything works
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Option Explicit

'==================================================================================
'  BASIC TESTS - RUN THESE FIRST
'==================================================================================

' QUICK TEST 1: Basic functionality test (RUN THIS FIRST)
Public Sub QuickTest_Basic()
    On Error GoTo TestError
    
    Debug.Print "=== QUICK TEST: Basic Functionality ==="
    Debug.Print "Time: " & Now()
    
    ' Test 1: Create worksheet
    Debug.Print "Step 1: Creating test worksheet..."
    Dim ws As Worksheet
    Set ws = CreateOrGetWorksheet("QuickTest")
    Debug.Print "✓ Worksheet created successfully"
    
    ' Test 2: Prepare canvas
    Debug.Print "Step 2: Preparing canvas..."
    Call PrepareCanvas(ws)
    Debug.Print "✓ Canvas prepared successfully"
    
    ' Test 3: Create single card
    Debug.Print "Step 3: Creating test card..."
    Dim config As StyledCardConfig
    config = CreateDefaultCardConfig("TestCard", 100, 100, "Test Status", "SUCCESS!")
    config.ValueFontColor = RGB(46, 204, 113)  ' Green for success
    
    Call CreateStyledCard(ws, config)
    Debug.Print "✓ Styled card created successfully"
    
    ' Test 4: Add title
    Debug.Print "Step 4: Adding dashboard title..."
    Call AddDashboardTitle(ws, "Quick Test Results", "Basic functionality test completed successfully")
    Debug.Print "✓ Title added successfully"
    
    ' Test 5: Activate sheet
    ws.Activate
    Debug.Print "✓ Sheet activated"
    
    Debug.Print "=== QUICK TEST COMPLETED SUCCESSFULLY ==="
    MsgBox "Quick Test PASSED!" & vbCrLf & vbCrLf & _
           "✓ All basic functions working correctly" & vbCrLf & _
           "✓ VBA compilation successful" & vbCrLf & _
           "✓ Card creation working" & vbCrLf & vbCrLf & _
           "Check the 'QuickTest' sheet to see your first styled card!", _
           vbInformation, "Test Successful"
    
    Exit Sub
    
TestError:
    Debug.Print "✗ ERROR in Quick Test: " & Err.Description
    MsgBox "Quick Test FAILED!" & vbCrLf & vbCrLf & _
           "Error: " & Err.Description & vbCrLf & vbCrLf & _
           "Please check that StyledCard_Core_Fixed.bas is imported correctly.", _
           vbCritical, "Test Failed"
End Sub

' QUICK TEST 2: Multi-card test
Public Sub QuickTest_MultiCard()
    On Error GoTo TestError
    
    Debug.Print "=== QUICK TEST: Multi-Card Dashboard ==="
    
    ' Create worksheet
    Dim ws As Worksheet
    Set ws = CreateOrGetWorksheet("QuickTest_Multi")
    
    ' Create 4 test cards
    Dim cards(3) As StyledCardConfig
    
    cards(0) = CreateDefaultCardConfig("Card1", 50, 120, "Test 1", "PASS")
    cards(0).CardFillColor = RGB(52, 152, 219)  ' Blue
    
    cards(1) = CreateDefaultCardConfig("Card2", 280, 120, "Test 2", "PASS") 
    cards(1).CardFillColor = RGB(46, 204, 113)  ' Green
    
    cards(2) = CreateDefaultCardConfig("Card3", 510, 120, "Test 3", "PASS")
    cards(2).CardFillColor = RGB(230, 126, 34)  ' Orange
    
    cards(3) = CreateDefaultCardConfig("Card4", 740, 120, "Test 4", "PASS")
    cards(3).CardFillColor = RGB(155, 89, 182)  ' Purple
    
    ' Build dashboard
    Call BuildStyledCardDashboard(ws, cards)
    Call AddDashboardTitle(ws, "Multi-Card Test Dashboard", "Testing multiple styled cards with different colors")
    
    ws.Activate
    
    Debug.Print "=== MULTI-CARD TEST COMPLETED SUCCESSFULLY ==="
    MsgBox "Multi-Card Test PASSED!" & vbCrLf & vbCrLf & _
           "✓ 4 styled cards created successfully" & vbCrLf & _
           "✓ Different colors applied correctly" & vbCrLf & _
           "✓ Dashboard assembly working" & vbCrLf & vbCrLf & _
           "Check the 'QuickTest_Multi' sheet!", _
           vbInformation, "Multi-Card Test Successful"
    
    Exit Sub
    
TestError:
    Debug.Print "✗ ERROR in Multi-Card Test: " & Err.Description
    MsgBox "Multi-Card Test FAILED: " & Err.Description, vbCritical, "Test Failed"
End Sub

' QUICK TEST 3: Formula test with sample data
Public Sub QuickTest_WithFormulas()
    On Error GoTo TestError
    
    Debug.Print "=== QUICK TEST: Formula Integration ==="
    
    ' Create sample data first
    Call SetupSampleFinancialData
    
    ' Create dashboard with real formulas
    Dim ws As Worksheet
    Set ws = CreateOrGetWorksheet("QuickTest_Formulas")
    
    Dim cards(2) As StyledCardConfig
    
    cards(0) = CreateDefaultCardConfig("Revenue", 50, 120, _
                                      "Total Revenue", "=TEXT(SUM(FinData!B:B),""$#,##0"")")
    
    cards(1) = CreateDefaultCardConfig("Expenses", 280, 120, _
                                      "Total Expenses", "=TEXT(SUM(FinData!C:C),""$#,##0"")")
    
    cards(2) = CreateDefaultCardConfig("Count", 510, 120, _
                                      "Data Points", "=COUNTA(FinData!A:A)-1")
    
    Call BuildStyledCardDashboard(ws, cards)
    Call AddDashboardTitle(ws, "Formula Test Dashboard", "Testing real Excel formulas in styled cards")
    
    ws.Activate
    
    Debug.Print "=== FORMULA TEST COMPLETED SUCCESSFULLY ==="
    MsgBox "Formula Test PASSED!" & vbCrLf & vbCrLf & _
           "✓ Excel formulas working in cards" & vbCrLf & _
           "✓ Sample data created successfully" & vbCrLf & _
           "✓ Dynamic data binding working" & vbCrLf & vbCrLf & _
           "Check the 'QuickTest_Formulas' sheet!", _
           vbInformation, "Formula Test Successful"
    
    Exit Sub
    
TestError:
    Debug.Print "✗ ERROR in Formula Test: " & Err.Description
    MsgBox "Formula Test FAILED: " & Err.Description, vbCritical, "Test Failed"
End Sub

'==================================================================================
'  COMPREHENSIVE TEST SUITE
'==================================================================================

' Run all quick tests in sequence
Public Sub RunAllQuickTests()
    On Error GoTo AllTestsError
    
    Debug.Print "=== RUNNING ALL QUICK TESTS ==="
    Debug.Print "Start Time: " & Now()
    
    Application.ScreenUpdating = False
    Application.DisplayAlerts = False
    
    ' Run all tests
    Call QuickTest_Basic
    Call QuickTest_MultiCard
    Call QuickTest_WithFormulas
    
    Application.DisplayAlerts = True
    Application.ScreenUpdating = True
    
    Debug.Print "=== ALL QUICK TESTS COMPLETED ==="
    Debug.Print "End Time: " & Now()
    
    MsgBox "ALL QUICK TESTS PASSED! 🎉" & vbCrLf & vbCrLf & _
           "✓ Basic functionality: WORKING" & vbCrLf & _
           "✓ Multi-card dashboards: WORKING" & vbCrLf & _
           "✓ Formula integration: WORKING" & vbCrLf & _
           "✓ VBA compilation: SUCCESSFUL" & vbCrLf & vbCrLf & _
           "The Styled Card system is ready for use!" & vbCrLf & vbCrLf & _
           "Check these sheets:" & vbCrLf & _
           "• QuickTest - Basic test" & vbCrLf & _
           "• QuickTest_Multi - Multi-card test" & vbCrLf & _
           "• QuickTest_Formulas - Formula test", _
           vbInformation, "All Tests Successful!"
    
    Exit Sub
    
AllTestsError:
    Application.DisplayAlerts = True
    Application.ScreenUpdating = True
    Debug.Print "✗ ERROR in test suite: " & Err.Description
    MsgBox "Test Suite FAILED: " & Err.Description, vbCritical, "Tests Failed"
End Sub

'==================================================================================
'  DIAGNOSTIC FUNCTIONS
'==================================================================================

' Check VBA environment and dependencies
Public Sub DiagnosticCheck()
    Debug.Print "=== DIAGNOSTIC CHECK ==="
    Debug.Print "Excel Version: " & Application.Version
    Debug.Print "VBA Version: " & Application.VBE.Version
    Debug.Print "Current Time: " & Now()
    
    ' Check if required functions are available
    On Error Resume Next
    
    Dim testWs As Worksheet
    Set testWs = CreateOrGetWorksheet("DiagnosticTest")
    If Err.Number = 0 Then
        Debug.Print "✓ CreateOrGetWorksheet function available"
    Else
        Debug.Print "✗ CreateOrGetWorksheet function NOT available"
        Debug.Print "  Error: " & Err.Description
    End If
    Err.Clear
    
    Dim testConfig As StyledCardConfig
    testConfig = CreateDefaultCardConfig("Test", 0, 0, "Test", "Test")
    If Err.Number = 0 Then
        Debug.Print "✓ CreateDefaultCardConfig function available"
    Else
        Debug.Print "✗ CreateDefaultCardConfig function NOT available"
        Debug.Print "  Error: " & Err.Description
    End If
    Err.Clear
    
    Call PrepareCanvas(testWs)
    If Err.Number = 0 Then
        Debug.Print "✓ PrepareCanvas function available"
    Else
        Debug.Print "✗ PrepareCanvas function NOT available"
        Debug.Print "  Error: " & Err.Description
    End If
    Err.Clear
    
    On Error GoTo 0
    
    Debug.Print "=== DIAGNOSTIC CHECK COMPLETE ==="
    
    MsgBox "Diagnostic check completed. See Debug window (Ctrl+G) for details.", vbInformation
End Sub

' Clean up all test sheets
Public Sub CleanupQuickTests()
    Dim testSheets As Variant
    testSheets = Array("QuickTest", "QuickTest_Multi", "QuickTest_Formulas", "DiagnosticTest", "FinData")
    
    Application.DisplayAlerts = False
    
    Dim i As Integer
    For i = LBound(testSheets) To UBound(testSheets)
        On Error Resume Next
        ThisWorkbook.Sheets(testSheets(i)).Delete
        On Error GoTo 0
    Next i
    
    Application.DisplayAlerts = True
    
    Debug.Print "Quick test cleanup completed"
    MsgBox "All quick test sheets have been removed.", vbInformation, "Cleanup Complete"
End Sub

'==================================================================================
'  STEP-BY-STEP GUIDED TEST
'==================================================================================

' Interactive step-by-step test for beginners
Public Sub GuidedTest_StepByStep()
    Dim response As VbMsgBoxResult
    
    ' Step 1
    response = MsgBox("Welcome to the Styled Card System!" & vbCrLf & vbCrLf & _
                     "This guided test will verify that everything is working correctly." & vbCrLf & vbCrLf & _
                     "Click OK to start the test, or Cancel to exit.", _
                     vbOKCancel + vbInformation, "Guided Test")
    
    If response = vbCancel Then Exit Sub
    
    ' Step 2
    Debug.Print "=== GUIDED TEST: Step-by-Step ==="
    Call QuickTest_Basic
    
    response = MsgBox("Step 1 completed! You should see a 'QuickTest' sheet with one styled card." & vbCrLf & vbCrLf & _
                     "Did you see the test card appear?", vbYesNo + vbQuestion, "Step 1 Complete")
    
    If response = vbNo Then
        MsgBox "Please check that the StyledCard_Core_Fixed.bas module is imported correctly.", vbExclamation
        Exit Sub
    End If
    
    ' Step 3
    Call QuickTest_MultiCard
    
    response = MsgBox("Step 2 completed! You should see a 'QuickTest_Multi' sheet with 4 colored cards." & vbCrLf & vbCrLf & _
                     "Did you see the 4 different colored cards?", vbYesNo + vbQuestion, "Step 2 Complete")
    
    If response = vbNo Then
        MsgBox "There may be an issue with the multi-card function. Check the Debug window for errors.", vbExclamation
        Exit Sub
    End If
    
    ' Step 4
    Call QuickTest_WithFormulas
    
    MsgBox "All tests completed successfully! 🎉" & vbCrLf & vbCrLf & _
           "✓ Basic card creation: WORKING" & vbCrLf & _
           "✓ Multi-card dashboards: WORKING" & vbCrLf & _
           "✓ Formula integration: WORKING" & vbCrLf & vbCrLf & _
           "The Styled Card Dashboard System is ready for use!" & vbCrLf & vbCrLf & _
           "You can now:" & vbCrLf & _
           "• Run the examples in Examples_Fixed.bas" & vbCrLf & _
           "• Create your own custom dashboards" & vbCrLf & _
           "• Modify the sample code for your needs", _
           vbInformation, "Guided Test Complete!"
End Sub
