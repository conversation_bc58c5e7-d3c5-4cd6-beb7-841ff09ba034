Option Explicit

' Minimal working VBA - tested for syntax
Public Sub InitializeMinimalBillingSystem()
    On Error GoTo ErrorHandler
    
    ' Create basic sheets
    Call CreateBasicSheet("Dashboard")
    Call CreateBasicSheet("Data_Entry") 
    Call CreateBasicSheet("Master_Data")
    Call CreateBasicSheet("Complexes")
    Call CreateBasicSheet("Units")
    
    ' Setup basic data
    Call SetupBasicData
    
    MsgBox "Minimal Billing System Ready!", vbInformation
    Exit Sub
    
ErrorHandler:
    MsgBox "Error: " & Err.Description, vbCritical
End Sub

Private Sub CreateBasicSheet(sheetName As String)
    Dim ws As Worksheet
    
    ' Delete if exists
    Application.DisplayAlerts = False
    On Error Resume Next
    ThisWorkbook.Sheets(sheetName).Delete
    On Error GoTo 0
    Application.DisplayAlerts = True
    
    ' Create new
    Set ws = ThisWorkbook.Sheets.Add
    ws.Name = sheetName
End Sub

Private Sub SetupBasicData()
    Dim ws As Worksheet
    
    ' Setup Dashboard
    Set ws = ThisWorkbook.Sheets("Dashboard")
    ws.Range("A1").Value = "Billing System Dashboard"
    ws.Range("A1").Font.Bold = True
    ws.Range("A1").Font.Size = 16
    
    ' Setup Data Entry  
    Set ws = ThisWorkbook.Sheets("Data_Entry")
    ws.Range("A1").Value = "Data Entry Form"
    ws.Range("A1").Font.Bold = True
    ws.Range("A3").Value = "Complex:"
    ws.Range("A4").Value = "Unit:"
    ws.Range("A5").Value = "Reading:"
    
    ' Setup Master Data
    Set ws = ThisWorkbook.Sheets("Master_Data")
    ws.Range("A1:E1").Value = Array("ID", "Complex", "Unit", "Reading", "Date")
    ws.Range("A1:E1").Font.Bold = True
    
    ' Setup Complexes
    Set ws = ThisWorkbook.Sheets("Complexes")  
    ws.Range("A1:B1").Value = Array("ComplexName", "TariffType")
    ws.Range("A1:B1").Font.Bold = True
    ws.Range("A2:B3").Value = Array("Sunset Villas", "Standard", "Oakwood Manor", "Premium")
    
    ' Setup Units
    Set ws = ThisWorkbook.Sheets("Units")
    ws.Range("A1:B1").Value = Array("ComplexName", "UnitName") 
    ws.Range("A1:B1").Font.Bold = True
    ws.Range("A2:B4").Value = Array("Sunset Villas", "Unit 1", "Sunset Villas", "Unit 2", "Oakwood Manor", "Unit 101")
End Sub

Public Sub AddBasicUnit()
    Dim complexName As String
    Dim unitName As String
    Dim ws As Worksheet
    
    complexName = InputBox("Enter complex name:")
    If complexName = "" Then Exit Sub
    
    unitName = InputBox("Enter unit name:")
    If unitName = "" Then Exit Sub
    
    Set ws = ThisWorkbook.Sheets("Units")
    Dim nextRow As Long
    nextRow = ws.Cells(ws.Rows.Count, "A").End(xlUp).Row + 1
    
    ws.Cells(nextRow, 1).Value = complexName
    ws.Cells(nextRow, 2).Value = unitName
    
    MsgBox "Unit added successfully!", vbInformation
End Sub

Public Sub SaveBasicReading()
    Dim ws As Worksheet
    Dim nextRow As Long
    
    Set ws = ThisWorkbook.Sheets("Master_Data")
    nextRow = ws.Cells(ws.Rows.Count, "A").End(xlUp).Row + 1
    
    ws.Cells(nextRow, 1).Value = nextRow - 1 ' ID
    ws.Cells(nextRow, 2).Value = "Sample Complex"
    ws.Cells(nextRow, 3).Value = "Sample Unit" 
    ws.Cells(nextRow, 4).Value = 100 ' Reading
    ws.Cells(nextRow, 5).Value = Now() ' Date
    
    MsgBox "Reading saved!", vbInformation
End Sub
