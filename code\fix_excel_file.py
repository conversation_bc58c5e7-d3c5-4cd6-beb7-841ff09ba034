"""
Fix Excel file creation - Create proper Excel format that Excel can open
"""
import openpyxl
from openpyxl.workbook import Workbook
from openpyxl.styles import Font, PatternFill, Alignment
import os

def create_proper_excel_file():
    """Create a proper Excel file that Excel can actually open"""
    print("Creating proper Excel file...")
    
    # Create new workbook
    wb = Workbook()
    
    # Remove default sheet
    if 'Sheet' in wb.sheetnames:
        wb.remove(wb['Sheet'])
    
    # Create main dashboard sheet
    dashboard_sheet = wb.create_sheet("Dashboard", 0)
    
    # Add styled header
    dashboard_sheet['A1'] = "STYLED CARD DASHBOARD SYSTEM"
    dashboard_sheet['A1'].font = Font(size=16, bold=True, color="FF0066CC")
    dashboard_sheet['A2'] = "✅ Ready for VBA import"
    dashboard_sheet['A2'].font = Font(size=12, color="FF00AA00")
    
    dashboard_sheet['A4'] = "📋 Status: VBA modules prepared and ready to import"
    dashboard_sheet['A5'] = "📖 Next step: Import VBA modules following setup instructions"
    dashboard_sheet['A6'] = "🧪 Test command: RunAllQuickTests"
    dashboard_sheet['A7'] = "🚀 First example: Example1_SimpleFinancialDashboard"
    
    # Set column width
    dashboard_sheet.column_dimensions['A'].width = 60
    
    # Create a data sheet for calculations
    data_sheet = wb.create_sheet("FinData", 1)
    
    # Add sample data with headers
    headers = ["Category", "Revenue", "Expenses"]
    for col, header in enumerate(headers, 1):
        cell = data_sheet.cell(row=1, column=col, value=header)
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color="FF4472C4", end_color="FF4472C4", fill_type="solid")
        cell.font = Font(bold=True, color="FFFFFFFF")
    
    # Sample financial data
    sample_data = [
        ("Q1 Sales", 150000, 85000),
        ("Q2 Sales", 175000, 92000),
        ("Q3 Sales", 185000, 95000),
        ("Q4 Sales", 195000, 98000),
        ("Marketing", 0, 45000),
        ("Operations", 0, 35000),
        ("Admin", 0, 25000)
    ]
    
    for row_idx, (category, revenue, expenses) in enumerate(sample_data, 2):
        data_sheet.cell(row=row_idx, column=1, value=category)
        data_sheet.cell(row=row_idx, column=2, value=revenue)
        data_sheet.cell(row=row_idx, column=3, value=expenses)
    
    # Auto-fit columns
    for col in ['A', 'B', 'C']:
        data_sheet.column_dimensions[col].width = 15
    
    # Create an instruction sheet
    instructions_sheet = wb.create_sheet("Setup_Instructions", 2)
    
    instructions = [
        ("STYLED CARD DASHBOARD SYSTEM - SETUP GUIDE", 16, True, "FF0066CC"),
        ("", 12, False, None),
        ("🚀 CRITICAL: This file needs VBA modules imported to work!", 12, True, "FFFF0000"),
        ("", 12, False, None),
        ("STEP 1: Save as Macro-Enabled", 14, True, "FF0066CC"),
        ("1. Click File → Save As", 11, False, None),
        ("2. Choose 'Excel Macro-Enabled Workbook (*.xlsm)' format", 11, False, None),
        ("3. Click Save", 11, False, None),
        ("", 12, False, None),
        ("STEP 2: Import VBA Modules (REQUIRED)", 14, True, "FF0066CC"),
        ("1. Press Alt+F11 to open VBA Editor", 11, False, None),
        ("2. Right-click 'VBAProject' → Import File", 11, False, None),
        ("3. Import these files IN ORDER from vba_modules folder:", 11, False, None),
        ("   a) StyledCard_Core_Fixed.bas (FIRST!)", 11, False, None),
        ("   b) QuickTest.bas", 11, False, None),
        ("   c) Examples_Fixed.bas", 11, False, None),
        ("", 12, False, None),
        ("STEP 3: Test Installation", 14, True, "FF0066CC"),
        ("1. In VBA Editor, press Ctrl+G (Immediate window)", 11, False, None),
        ("2. Type: RunAllQuickTests", 11, False, None),
        ("3. Press Enter", 11, False, None),
        ("4. Should see: 'ALL QUICK TESTS PASSED! 🎉'", 11, False, None),
        ("", 12, False, None),
        ("STEP 4: Create First Dashboard", 14, True, "FF0066CC"),
        ("1. In Immediate window, type: Example1_SimpleFinancialDashboard", 11, False, None),
        ("2. Press Enter", 11, False, None),
        ("3. Check new 'Example1_Financial' sheet", 11, False, None),
        ("", 12, False, None),
        ("📁 VBA FILES LOCATION:", 14, True, "FF0066CC"),
        ("- Look for 'vba_modules' folder with this Excel file", 11, False, None),
        ("- Contains 3 .bas files to import", 11, False, None),
        ("", 12, False, None),
        ("🧪 AVAILABLE COMMANDS (run in VBA Immediate window):", 14, True, "FF0066CC"),
        ("", 12, False, None),
        ("Testing Commands:", 12, True, "FF008000"),
        ("- RunAllQuickTests", 11, False, None),
        ("- QuickTest_Basic", 11, False, None),
        ("- QuickTest_MultiCard", 11, False, None),
        ("- QuickTest_WithFormulas", 11, False, None),
        ("", 12, False, None),
        ("Example Commands:", 12, True, "FF008000"),
        ("- Example1_SimpleFinancialDashboard", 11, False, None),
        ("- Example2_GridLayoutDashboard", 11, False, None),
        ("- Example3_ThemedDashboard", 11, False, None),
        ("- Example4_StaticTextDashboard", 11, False, None),
        ("- Example5_CompleteDashboard", 11, False, None),
        ("", 12, False, None),
        ("🛠️ TROUBLESHOOTING:", 14, True, "FFFF6600"),
        ("- If commands don't work: Check VBA modules are imported", 11, False, None),
        ("- If cards don't show: Make sure you saved as .xlsm first", 11, False, None),
        ("- If errors occur: Run QuickTest_Basic first", 11, False, None),
        ("", 12, False, None),
        ("✅ SUCCESS INDICATORS:", 14, True, "FF008000"),
        ("- Test command shows 'ALL QUICK TESTS PASSED!'", 11, False, None),
        ("- New sheets created with styled cards", 11, False, None),
        ("- Professional dashboard appearance", 11, False, None)
    ]
    
    for row_idx, instruction_data in enumerate(instructions, 1):
        text, size, bold, color = instruction_data
        cell = instructions_sheet.cell(row=row_idx, column=1, value=text)
        cell.font = Font(size=size, bold=bold, color=color or "FF000000")
        cell.alignment = Alignment(wrap_text=True)
    
    # Auto-fit column width
    instructions_sheet.column_dimensions['A'].width = 80
    
    # Save as regular Excel file first
    excel_path = "/workspace/excel_output/StyledCard_Dashboard_System.xlsx"
    wb.save(excel_path)
    
    print(f"✓ Proper Excel file created: {excel_path}")
    return excel_path

def create_quick_start_file():
    """Create a simple text file with quick start instructions"""
    quick_start = """
🚀 STYLED CARD DASHBOARD SYSTEM - QUICK START

📁 WHAT YOU HAVE:
- StyledCard_Dashboard_System.xlsx (Excel file)
- vba_modules/ folder (VBA code files)
- This instruction file

⚡ QUICK SETUP (3 steps):

1️⃣ OPEN EXCEL FILE
   - Open StyledCard_Dashboard_System.xlsx
   - Click File → Save As → Excel Macro-Enabled Workbook (.xlsm)
   - Save it (this enables macro support)

2️⃣ IMPORT VBA CODE
   - Press Alt+F11 (VBA Editor)
   - Right-click "VBAProject" → Import File
   - Import these 3 files IN ORDER:
     * StyledCard_Core_Fixed.bas (FIRST!)
     * QuickTest.bas  
     * Examples_Fixed.bas

3️⃣ TEST IT WORKS
   - In VBA Editor: Ctrl+G (Immediate window)
   - Type: RunAllQuickTests
   - Press Enter
   - Should see: "ALL QUICK TESTS PASSED! 🎉"

🎉 CREATE YOUR FIRST DASHBOARD:
   - Type: Example1_SimpleFinancialDashboard
   - Press Enter
   - Check new sheet with professional cards!

⚠️ IMPORTANT:
- You MUST save as .xlsm format first
- Import VBA files in the exact order shown
- Test before creating dashboards

✅ SUCCESS = Professional styled cards instead of plain Excel tables!
"""
    
    with open('/workspace/excel_output/QUICK_START.txt', 'w', encoding='utf-8') as f:
        f.write(quick_start)
    
    print("✓ Quick start file created")

def main():
    """Main function to create proper Excel file"""
    print("🔧 Fixing Excel file format issue...")
    
    # Create proper Excel file
    excel_path = create_proper_excel_file()
    
    # Create quick start guide
    create_quick_start_file()
    
    # Verify file exists and get size
    if os.path.exists(excel_path):
        size = os.path.getsize(excel_path)
        print(f"✓ Excel file verified: {size:,} bytes")
    
    print("\n🎉 FIXED! Excel file should now open properly!")
    print("📋 Next steps:")
    print("1. Open StyledCard_Dashboard_System.xlsx")
    print("2. Save as .xlsm format (macro-enabled)")
    print("3. Import VBA modules as instructed")
    print("4. Test with RunAllQuickTests")
    
    return excel_path

if __name__ == "__main__":
    main()
