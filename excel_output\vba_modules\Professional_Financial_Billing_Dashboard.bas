
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
'~          PROFESSIONAL WATER METER BILLING DASHBOARD SYSTEM
'~
'~ Visual Style: Professional Excel Financial Dashboard (YouTube Reference)
'~ Features: Dark theme, interactive charts, KPI cards, financial analytics style
'~
'~ Core Purpose: Smart water meter billing with manual fallback + Professional UI
'~ Author: MiniMax Agent
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Option Explicit

' --- Microsoft Office Constants for Compatibility ---
Private Const MSO_TRUE As Long = -1
Private Const MSO_SHADOW_OFFSET As Long = 2
Private Const MSO_SHAPE_ROUNDED_RECTANGLE As Long = 5
Private Const MSO_TEXT_ORIENTATION_HORIZONTAL As Long = 1

' --- Professional Financial Dashboard Color Scheme (YouTube Style) ---
Public Const DASHBOARD_DARK_BACKGROUND As Long = 2829353     ' Dark charcoal #2B2B29
Public Const DASHBOARD_PANEL_COLOR As Long = 3618615        ' Dark blue-grey #373B37
Public Const DASHBOARD_CARD_COLOR As Long = 4144959         ' Professional blue-grey #434343
Public Const DASHBOARD_ACCENT_BLUE As Long = 16711680       ' Financial blue #0066FF
Public Const DASHBOARD_ACCENT_GREEN As Long = 5287936       ' Success green #52C41A
Public Const DASHBOARD_ACCENT_ORANGE As Long = 37119        ' Warning orange #FF9800
Public Const DASHBOARD_ACCENT_RED As Long = 255             ' Alert red #FF0000
Public Const DASHBOARD_TEXT_WHITE As Long = 16777215        ' Clean white #FFFFFF
Public Const DASHBOARD_TEXT_LIGHT As Long = 12632256        ' Light grey #C0C0C0
Public Const DASHBOARD_BORDER_COLOR As Long = 5592405       ' Subtle border #555555

' --- Billing System Constants ---
Public Const DASHBOARD_NAME As String = "Financial_Billing_Dashboard"
Public Const DATABASE_NAME As String = "Meter_Data"
Public Const COMPLEXES_SHEET_NAME As String = "Complexes"
Public Const UNITS_SHEET_NAME As String = "Unit_List"
Public Const TARIFF_STRUCTURES_NAME As String = "Tariff_Structures"
Public Const FIXED_CHARGES_NAME As String = "Fixed_Charges"
Public Const DATA_ENTRY_SHEET_NAME As String = "Meter_Readings"
Public Const BILL_TEMPLATE_NAME As String = "Professional_Bills"

' --- KPI Card Dimensions (Financial Dashboard Style) ---
Public Const KPI_CARD_WIDTH As Double = 200
Public Const KPI_CARD_HEIGHT As Double = 100
Public Const CHART_PANEL_WIDTH As Double = 400
Public Const CHART_PANEL_HEIGHT As Double = 250

' --- Billing Calculation Structure ---
Public Type BillCalculationResult
    subTotal As Double
    vatAmount As Double
    totalDue As Double
    totalFixedCharges As Double
    billConsumption As Double
    MechConsumption As Double
    digitalConsumption As Double
    numberOfMonths As Long
    AverageMonthlyConsumption As Double
    TariffBreakdown As String
End Type

' --- Professional Dashboard Card Configuration ---
Public Type FinancialDashboardCard
    XPosition As Double
    YPosition As Double
    Width As Double
    Height As Double
    TitleText As String
    ValueFormula As String
    ValueFormat As String
    CardColor As Long
    AccentColor As Long
    IconText As String
    CardID As String
End Type

'==================================================================================
'  MAIN SYSTEM INITIALIZATION - PROFESSIONAL FINANCIAL STYLE
'==================================================================================

Public Sub InitializeProfessionalBillingDashboard()
    On Error GoTo ErrorHandler
    Application.ScreenUpdating = False
    
    ' Create all billing system sheets
    Call CreateBillingSystemSheets
    
    ' Setup billing data structures
    Call SetupBillingDataStructures
    
    ' Create the professional financial dashboard
    Call CreateProfessionalFinancialDashboard
    
    ' Setup professional data entry interface
    Call CreateProfessionalDataEntry
    
    ' Create professional bill templates
    Call CreateProfessionalBillTemplate
    
    ThisWorkbook.Sheets(DASHBOARD_NAME).Activate
    Application.ScreenUpdating = True
    
    MsgBox "Professional Water Meter Billing System Initialized!" & vbCrLf & _
           "Style: Financial Dashboard (YouTube Reference)" & vbCrLf & _
           "Features:" & vbCrLf & _
           "✓ Dark Professional Theme" & vbCrLf & _
           "✓ Interactive KPI Cards" & vbCrLf & _
           "✓ Financial Analytics Style" & vbCrLf & _
           "✓ Smart Meter + Manual Billing" & vbCrLf & _
           "✓ IBT Tariff Calculations", vbInformation, "Professional Billing Dashboard"
    Exit Sub
    
ErrorHandler:
    Application.ScreenUpdating = True
    MsgBox "Error initializing professional billing dashboard: " & Err.Description, vbCritical
End Sub

'==================================================================================
'  PROFESSIONAL FINANCIAL DASHBOARD CREATION
'==================================================================================

Private Sub CreateProfessionalFinancialDashboard()
    Dim ws As Worksheet
    Set ws = ThisWorkbook.Sheets(DASHBOARD_NAME)
    
    ' Apply professional dark theme to entire sheet
    Call ApplyProfessionalDarkTheme(ws)
    
    ' Create dashboard header with logo area
    Call CreateDashboardHeader(ws)
    
    ' Create KPI cards in financial dashboard style
    Call CreateFinancialKPICards(ws)
    
    ' Create interactive charts panel
    Call CreateInteractiveChartsPanel(ws)
    
    ' Create professional control panel
    Call CreateProfessionalControlPanel(ws)
    
    ' Create data filtering section
    Call CreateProfessionalFilterPanel(ws)
    
    ' Create recent activity feed
    Call CreateRecentActivityFeed(ws)
End Sub

' Apply professional dark theme (YouTube financial dashboard style)
Private Sub ApplyProfessionalDarkTheme(ws As Worksheet)
    ' Set worksheet background to professional dark color
    With ws.Cells.Interior
        .Color = DASHBOARD_DARK_BACKGROUND
        .Pattern = xlSolid
    End With
    
    ' Hide gridlines and headers for clean look
    With Application.ActiveWindow
        .DisplayGridlines = False
        .DisplayHeadings = False
    End With
    
    ' Set optimal column widths for dashboard layout
    ws.Columns("A").ColumnWidth = 2
    ws.Columns("B:H").ColumnWidth = 15
    ws.Columns("I:O").ColumnWidth = 12
    ws.Columns("P").ColumnWidth = 2
End Sub

' Create professional dashboard header
Private Sub CreateDashboardHeader(ws As Worksheet)
    ' Create header background panel
    Call CreateFinancialPanel(ws, "HeaderPanel", 20, 10, 1200, 60, DASHBOARD_PANEL_COLOR, "")
    
    ' Add company logo area (placeholder)
    Call CreateFinancialPanel(ws, "LogoPanel", 30, 20, 80, 40, DASHBOARD_ACCENT_BLUE, "LOGO")
    
    ' Add main dashboard title
    Call CreateFinancialText(ws, "DashboardTitle", 130, 25, 400, 30, _
                            "WATER METER BILLING SYSTEM", 18, True, DASHBOARD_TEXT_WHITE)
    
    ' Add subtitle
    Call CreateFinancialText(ws, "DashboardSubtitle", 130, 45, 400, 15, _
                            "Professional Financial Dashboard", 10, False, DASHBOARD_TEXT_LIGHT)
    
    ' Add current date/time indicator
    Call CreateFinancialText(ws, "DateTime", 950, 25, 250, 20, _
                            "=TEXT(NOW(),""DD MMM YYYY HH:MM"")", 10, False, DASHBOARD_TEXT_LIGHT)
    
    ' Add system status indicator
    Call CreateStatusIndicator(ws, 950, 45, "OPERATIONAL", DASHBOARD_ACCENT_GREEN)
End Sub

' Create financial KPI cards (YouTube dashboard style)
Private Sub CreateFinancialKPICards(ws As Worksheet)
    Dim card1 As FinancialDashboardCard, card2 As FinancialDashboardCard
    Dim card3 As FinancialDashboardCard, card4 As FinancialDashboardCard
    
    ' KPI Card 1: Total Revenue
    card1 = CreateFinancialCardConfig("TotalRevenue", 50, 90, _
                                     "TOTAL REVENUE", "=SUMPRODUCT(Meter_Data!N:N)", _
                                     "$#,##0", DASHBOARD_ACCENT_BLUE, "💰")
    Call CreateFinancialKPICard(ws, card1)
    
    ' KPI Card 2: Active Units
    card2 = CreateFinancialCardConfig("ActiveUnits", 270, 90, _
                                     "ACTIVE UNITS", "=COUNTA(Unit_List!B:B)-1", _
                                     "#,##0", DASHBOARD_ACCENT_GREEN, "🏢")
    Call CreateFinancialKPICard(ws, card2)
    
    ' KPI Card 3: Pending Bills
    card3 = CreateFinancialCardConfig("PendingBills", 490, 90, _
                                     "PENDING BILLS", "=COUNTIF(Meter_Data!O:O,""Pending Bill"")", _
                                     "#,##0", DASHBOARD_ACCENT_ORANGE, "📋")
    Call CreateFinancialKPICard(ws, card3)
    
    ' KPI Card 4: Collection Rate
    card4 = CreateFinancialCardConfig("CollectionRate", 710, 90, _
                                     "COLLECTION RATE", "85.5", _
                                     "#0.0""%""", DASHBOARD_ACCENT_GREEN, "📈")
    Call CreateFinancialKPICard(ws, card4)
    
    ' KPI Card 5: Monthly Average
    Dim card5 As FinancialDashboardCard
    card5 = CreateFinancialCardConfig("MonthlyAverage", 930, 90, _
                                     "AVG MONTHLY USAGE", "=AVERAGE(Meter_Data!U:U)", _
                                     "#,##0"" kL""", DASHBOARD_ACCENT_BLUE, "💧")
    Call CreateFinancialKPICard(ws, card5)
End Sub

' Create individual financial KPI card
Private Sub CreateFinancialKPICard(ws As Worksheet, config As FinancialDashboardCard)
    ' Create card background with professional styling
    Call CreateFinancialPanel(ws, config.CardID & "_Base", config.XPosition, config.YPosition, _
                             config.Width, config.Height, DASHBOARD_CARD_COLOR, "")
    
    ' Add accent color strip
    Call CreateFinancialPanel(ws, config.CardID & "_Accent", config.XPosition, config.YPosition, _
                             5, config.Height, config.AccentColor, "")
    
    ' Add icon area
    Call CreateFinancialText(ws, config.CardID & "_Icon", config.XPosition + 15, config.YPosition + 10, _
                            30, 30, config.IconText, 16, False, config.AccentColor)
    
    ' Add title text
    Call CreateFinancialText(ws, config.CardID & "_Title", config.XPosition + 15, config.YPosition + 45, _
                            config.Width - 30, 15, config.TitleText, 9, False, DASHBOARD_TEXT_LIGHT)
    
    ' Add value text
    Call CreateFinancialText(ws, config.CardID & "_Value", config.XPosition + 15, config.YPosition + 15, _
                            config.Width - 50, 25, config.ValueFormula, 16, True, DASHBOARD_TEXT_WHITE)
    
    ' Add trend indicator (placeholder)
    Call CreateFinancialText(ws, config.CardID & "_Trend", config.XPosition + config.Width - 30, _
                            config.YPosition + 65, 25, 15, "↗", 12, False, DASHBOARD_ACCENT_GREEN)
End Sub

' Create interactive charts panel (financial dashboard style)
Private Sub CreateInteractiveChartsPanel(ws As Worksheet)
    ' Main charts container
    Call CreateFinancialPanel(ws, "ChartsPanel", 50, 210, 600, 300, DASHBOARD_PANEL_COLOR, "")
    
    ' Chart title
    Call CreateFinancialText(ws, "ChartsTitle", 70, 230, 200, 20, _
                            "CONSUMPTION ANALYTICS", 12, True, DASHBOARD_TEXT_WHITE)
    
    ' Placeholder for consumption trend chart
    Call CreateFinancialPanel(ws, "ConsumptionChart", 70, 250, 560, 200, DASHBOARD_CARD_COLOR, "")
    Call CreateFinancialText(ws, "ChartPlaceholder", 300, 340, 100, 20, _
                            "📊 Consumption Trends", 10, False, DASHBOARD_TEXT_LIGHT)
    
    ' Chart controls
    Call CreateFinancialButton(ws, 70, 460, 100, 25, "Monthly View", "ShowMonthlyChart")
    Call CreateFinancialButton(ws, 180, 460, 100, 25, "Yearly View", "ShowYearlyChart")
    Call CreateFinancialButton(ws, 290, 460, 100, 25, "By Complex", "ShowComplexChart")
End Sub

' Create professional control panel
Private Sub CreateProfessionalControlPanel(ws As Worksheet)
    ' Control panel container
    Call CreateFinancialPanel(ws, "ControlPanel", 670, 210, 280, 300, DASHBOARD_PANEL_COLOR, "")
    
    ' Control panel title
    Call CreateFinancialText(ws, "ControlTitle", 690, 230, 200, 20, _
                            "BILLING OPERATIONS", 12, True, DASHBOARD_TEXT_WHITE)
    
    ' Professional styled buttons
    Call CreateFinancialButton(ws, 690, 260, 240, 30, "📊 Enter Meter Reading", "ShowMeterEntry")
    Call CreateFinancialButton(ws, 690, 300, 240, 30, "🏢 Manage Complexes", "ManageComplexes")
    Call CreateFinancialButton(ws, 690, 340, 240, 30, "💰 Manage Tariffs", "ManageTariffs")
    Call CreateFinancialButton(ws, 690, 380, 240, 30, "📋 Generate Bills", "GenerateBills")
    Call CreateFinancialButton(ws, 690, 420, 240, 30, "📈 View Reports", "ViewReports")
    Call CreateFinancialButton(ws, 690, 460, 240, 30, "🔄 Refresh Data", "RefreshBillingDashboard")
End Sub

' Create professional filter panel
Private Sub CreateProfessionalFilterPanel(ws As Worksheet)
    ' Filter panel container
    Call CreateFinancialPanel(ws, "FilterPanel", 970, 210, 250, 150, DASHBOARD_PANEL_COLOR, "")
    
    ' Filter title
    Call CreateFinancialText(ws, "FilterTitle", 990, 230, 150, 20, _
                            "DATA FILTERS", 12, True, DASHBOARD_TEXT_WHITE)
    
    ' Complex filter
    Call CreateFinancialText(ws, "ComplexLabel", 990, 260, 100, 15, _
                            "Complex:", 9, False, DASHBOARD_TEXT_LIGHT)
    ' Dropdown placeholder
    Call CreateFinancialPanel(ws, "ComplexDropdown", 990, 275, 200, 20, DASHBOARD_CARD_COLOR, "")
    
    ' Date range filter
    Call CreateFinancialText(ws, "DateLabel", 990, 305, 100, 15, _
                            "Date Range:", 9, False, DASHBOARD_TEXT_LIGHT)
    Call CreateFinancialPanel(ws, "DateFromDropdown", 990, 320, 95, 20, DASHBOARD_CARD_COLOR, "")
    Call CreateFinancialPanel(ws, "DateToDropdown", 1095, 320, 95, 20, DASHBOARD_CARD_COLOR, "")
    
    ' Apply filter button
    Call CreateFinancialButton(ws, 990, 350, 200, 25, "Apply Filters", "ApplyDashboardFilters")
End Sub

' Create recent activity feed
Private Sub CreateRecentActivityFeed(ws As Worksheet)
    ' Activity feed container
    Call CreateFinancialPanel(ws, "ActivityPanel", 970, 380, 250, 130, DASHBOARD_PANEL_COLOR, "")
    
    ' Activity title
    Call CreateFinancialText(ws, "ActivityTitle", 990, 400, 150, 20, _
                            "RECENT ACTIVITY", 12, True, DASHBOARD_TEXT_WHITE)
    
    ' Sample activity items
    Call CreateFinancialText(ws, "Activity1", 990, 425, 200, 12, _
                            "• Sunset Villas - Unit A1 reading", 8, False, DASHBOARD_TEXT_LIGHT)
    Call CreateFinancialText(ws, "Activity2", 990, 440, 200, 12, _
                            "• Bill generated - Complex B", 8, False, DASHBOARD_TEXT_LIGHT)
    Call CreateFinancialText(ws, "Activity3", 990, 455, 200, 12, _
                            "• Tariff updated - Residential", 8, False, DASHBOARD_TEXT_LIGHT)
    Call CreateFinancialText(ws, "Activity4", 990, 470, 200, 12, _
                            "• Payment received - Unit 101", 8, False, DASHBOARD_TEXT_LIGHT)
    Call CreateFinancialText(ws, "Activity5", 990, 485, 200, 12, _
                            "• System backup completed", 8, False, DASHBOARD_TEXT_LIGHT)
End Sub

'==================================================================================
'  PROFESSIONAL UI COMPONENTS (FINANCIAL DASHBOARD STYLE)
'==================================================================================

' Create financial dashboard panel
Private Sub CreateFinancialPanel(ws As Worksheet, name As String, left As Double, top As Double, _
                                width As Double, height As Double, fillColor As Long, titleText As String)
    Dim shp As Shape
    
    On Error Resume Next
    ws.Shapes(name).Delete
    On Error GoTo 0
    
    Set shp = ws.Shapes.AddShape(MSO_SHAPE_ROUNDED_RECTANGLE, left, top, width, height)
    shp.Name = name
    
    With shp
        .Fill.ForeColor.RGB = fillColor
        .Fill.Transparency = 0
        
        .Line.ForeColor.RGB = DASHBOARD_BORDER_COLOR
        .Line.Weight = 0.5
        .Line.Transparency = 0.3
        
        ' Add subtle shadow for depth
        On Error Resume Next
        With .Shadow
            .Type = MSO_SHADOW_OFFSET
            .OffsetX = 2
            .OffsetY = 2
            .Blur = 6
            .Transparency = 0.7
            .ForeColor.RGB = RGB(0, 0, 0)
        End With
        On Error GoTo 0
        
        ' Rounded corners
        On Error Resume Next
        .Adjustments(1) = 0.1
        On Error GoTo 0
    End With
    
    If titleText <> "" Then
        shp.TextFrame.Characters.Text = titleText
        With shp.TextFrame.Characters.Font
            .Bold = True
            .Size = 10
            .Color = DASHBOARD_TEXT_WHITE
        End With
        shp.TextFrame.HorizontalAlignment = xlHAlignCenter
        shp.TextFrame.VerticalAlignment = xlVAlignTop
        shp.TextFrame.MarginTop = 8
    End If
End Sub

' Create financial dashboard text
Private Sub CreateFinancialText(ws As Worksheet, name As String, left As Double, top As Double, _
                               width As Double, height As Double, text As String, fontSize As Integer, _
                               isBold As Boolean, fontColor As Long)
    Dim shp As Shape
    
    On Error Resume Next
    ws.Shapes(name).Delete
    On Error GoTo 0
    
    Set shp = ws.Shapes.AddTextbox(MSO_TEXT_ORIENTATION_HORIZONTAL, left, top, width, height)
    shp.Name = name
    
    With shp.TextFrame
        .Characters.Text = text
        .Characters.Font.Name = "Segoe UI"
        .Characters.Font.Size = fontSize
        .Characters.Font.Bold = isBold
        .Characters.Font.Color = fontColor
        .HorizontalAlignment = xlHAlignLeft
        .VerticalAlignment = xlVAlignCenter
        .MarginLeft = 0
        .MarginRight = 0
        .MarginTop = 0
        .MarginBottom = 0
    End With
    
    With shp
        .Fill.Transparency = 1
        .Line.Transparency = 1
    End With
End Sub

' Create financial dashboard button
Private Sub CreateFinancialButton(ws As Worksheet, left As Double, top As Double, _
                                 width As Double, height As Double, caption As String, macroName As String)
    Dim btn As Button
    
    ' Remove existing button at same position
    On Error Resume Next
    Dim existingBtn As Button
    For Each existingBtn In ws.Buttons
        If Abs(existingBtn.Left - left) < 5 And Abs(existingBtn.Top - top) < 5 Then
            existingBtn.Delete
            Exit For
        End If
    Next existingBtn
    On Error GoTo 0
    
    ' Create new styled button
    Set btn = ws.Buttons.Add(left, top, width, height)
    btn.Text = caption
    btn.OnAction = macroName
    
    With btn.Font
        .Name = "Segoe UI"
        .Size = 9
        .Bold = True
        .Color = DASHBOARD_TEXT_WHITE
    End With
    
    ' Style the button (limited in Excel VBA)
    On Error Resume Next
    btn.Interior.Color = DASHBOARD_ACCENT_BLUE
    btn.Border.Color = DASHBOARD_BORDER_COLOR
    On Error GoTo 0
End Sub

' Create status indicator
Private Sub CreateStatusIndicator(ws As Worksheet, left As Double, top As Double, _
                                 statusText As String, statusColor As Long)
    ' Status indicator circle
    Dim indicator As Shape
    Set indicator = ws.Shapes.AddShape(1, left, top, 12, 12) ' Circle
    
    With indicator
        .Name = "StatusIndicator"
        .Fill.ForeColor.RGB = statusColor
        .Line.Transparency = 1
    End With
    
    ' Status text
    Call CreateFinancialText(ws, "StatusText", left + 18, top - 2, 100, 16, _
                            statusText, 8, False, statusColor)
End Sub

' Create financial card configuration
Private Function CreateFinancialCardConfig(cardID As String, x As Double, y As Double, _
                                          titleText As String, valueFormula As String, _
                                          valueFormat As String, accentColor As Long, _
                                          iconText As String) As FinancialDashboardCard
    Dim config As FinancialDashboardCard
    
    config.XPosition = x
    config.YPosition = y
    config.Width = KPI_CARD_WIDTH
    config.Height = KPI_CARD_HEIGHT
    config.TitleText = titleText
    config.ValueFormula = valueFormula
    config.ValueFormat = valueFormat
    config.CardColor = DASHBOARD_CARD_COLOR
    config.AccentColor = accentColor
    config.IconText = iconText
    config.CardID = cardID
    
    CreateFinancialCardConfig = config
End Function

'==================================================================================
'  BILLING SYSTEM CORE FUNCTIONS (PRESERVED FROM ORIGINAL)
'==================================================================================

' Calculate billing with IBT tariff structure
Private Function CalculateBillValues(ByVal prevReading As Double, ByVal currReading As Double, _
                                   ByVal digitalConsumption As Double, ByVal prevDate As Date, _
                                   ByVal currDate As Date, ByVal complexName As String) As BillCalculationResult
    
    Dim Result As BillCalculationResult
    Dim compWs As Worksheet: Set compWs = ThisWorkbook.Sheets(COMPLEXES_SHEET_NAME)
    Dim tariffWs As Worksheet: Set tariffWs = ThisWorkbook.Sheets(TARIFF_STRUCTURES_NAME)
    Dim fixedWs As Worksheet: Set fixedWs = ThisWorkbook.Sheets(FIXED_CHARGES_NAME)
    
    ' Calculate time span and consumption
    Result.numberOfMonths = CalculateMonths(prevDate, currDate)
    If Result.numberOfMonths < 1 Then Result.numberOfMonths = 1
    
    Result.MechConsumption = Abs(currReading - prevReading)
    Result.digitalConsumption = digitalConsumption
    Result.billConsumption = Result.MechConsumption - digitalConsumption
    If Result.billConsumption < 0 Then Result.billConsumption = 0
    
    Result.AverageMonthlyConsumption = Result.billConsumption / Result.numberOfMonths
    
    ' Get complex configuration and calculate charges
    Dim compRow As Range
    Set compRow = compWs.Columns("A").Find(complexName, LookIn:=xlValues, LookAt:=xlWhole)
    If compRow Is Nothing Then Exit Function
    
    ' Calculate fixed charges
    Dim fixedCharge1Name As String: fixedCharge1Name = compRow.Offset(0, 2).Value
    Dim fixedCharge2Name As String: fixedCharge2Name = compRow.Offset(0, 3).Value
    Dim fixedCharge1 As Double, fixedCharge2 As Double
    
    If fixedCharge1Name <> "" Then
        Dim fc1Row As Range
        Set fc1Row = fixedWs.Columns("A").Find(fixedCharge1Name, LookIn:=xlValues, LookAt:=xlWhole)
        If Not fc1Row Is Nothing Then fixedCharge1 = fc1Row.Offset(0, 1).Value
    End If
    
    If fixedCharge2Name <> "" Then
        Dim fc2Row As Range
        Set fc2Row = fixedWs.Columns("A").Find(fixedCharge2Name, LookIn:=xlValues, LookAt:=xlWhole)
        If Not fc2Row Is Nothing Then fixedCharge2 = fc2Row.Offset(0, 1).Value
    End If
    
    Result.totalFixedCharges = (fixedCharge1 + fixedCharge2) * Result.numberOfMonths
    
    ' Calculate consumption charges using IBT
    Dim tariffName As String: tariffName = compRow.Offset(0, 1).Value
    Dim tariffRow As Range
    Set tariffRow = tariffWs.Columns("A").Find(tariffName, LookIn:=xlValues, LookAt:=xlWhole)
    If tariffRow Is Nothing Then Exit Function
    
    Dim rateType As String: rateType = tariffRow.Offset(0, 1).Value
    Dim TotalConsumptionCharges As Double
    
    If rateType = "Flat" Then
        Dim flatRate As Double: flatRate = tariffRow.Offset(0, 2).Value
        TotalConsumptionCharges = Result.billConsumption * flatRate
        Result.TariffBreakdown = "Flat Rate: " & Result.billConsumption & " x " & FormatCurrency(flatRate, 2)
    ElseIf rateType = "IBT" Then
        TotalConsumptionCharges = CalculateIBT(Result.billConsumption, tariffRow)
        Result.TariffBreakdown = BuildIBTBreakdownString(Result.billConsumption, tariffRow)
    End If
    
    ' Final calculation with VAT
    Result.subTotal = TotalConsumptionCharges + Result.totalFixedCharges
    Dim vatRate As Double: vatRate = 0.15 ' 15% VAT
    Result.vatAmount = Result.subTotal * vatRate
    Result.totalDue = Result.subTotal + Result.vatAmount
    
    CalculateBillValues = Result
End Function

' Calculate IBT charges
Private Function CalculateIBT(consumption As Double, profileRow As Range) As Double
    Dim totalCost As Double, i As Long, blockEnd As Double, blockRate As Double
    Dim prevEnd As Double, used As Double
    
    totalCost = 0: prevEnd = 0
    
    For i = 1 To 5
        blockEnd = profileRow.Offset(0, 2 + (i - 1) * 2).Value
        blockRate = profileRow.Offset(0, 3 + (i - 1) * 2).Value
        
        If blockEnd = 0 Then Exit For
        
        If consumption > prevEnd Then
            used = Application.Min(consumption, blockEnd) - prevEnd
            If used > 0 Then totalCost = totalCost + used * blockRate
            prevEnd = blockEnd
        Else
            Exit For
        End If
    Next i
    
    CalculateIBT = totalCost
End Function

' Build IBT breakdown string
Private Function BuildIBTBreakdownString(consumption As Double, profileRow As Range) As String
    Dim breakdown As String, i As Long, blockEnd As Double, blockRate As Double
    Dim prevEnd As Double, used As Double, blockCost As Double
    
    prevEnd = 0: breakdown = ""
    
    For i = 1 To 5
        blockEnd = profileRow.Offset(0, 2 + (i - 1) * 2).Value
        blockRate = profileRow.Offset(0, 3 + (i - 1) * 2).Value
        
        If blockEnd = 0 Then Exit For
        
        If consumption > prevEnd Then
            used = Application.Min(consumption, blockEnd) - prevEnd
            If used > 0 Then
                blockCost = used * blockRate
                breakdown = breakdown & "Block " & i & ": " & Format(used, "0.00") & _
                           " x " & FormatCurrency(blockRate, 2) & " = " & _
                           FormatCurrency(blockCost, 2) & vbCrLf
            End If
            prevEnd = blockEnd
        Else
            Exit For
        End If
    Next i
    
    BuildIBTBreakdownString = Left(breakdown, Len(breakdown) - 2)
End Function

' Calculate months between dates
Private Function CalculateMonths(StartDate As Date, EndDate As Date) As Integer
    If EndDate < StartDate Then
        CalculateMonths = 0
        Exit Function
    End If
    
    CalculateMonths = DateDiff("m", StartDate, EndDate)
    If CalculateMonths = 0 Then CalculateMonths = 1
End Function

'==================================================================================
'  USER INTERFACE FUNCTIONS (PROFESSIONAL STYLE)
'==================================================================================

Public Sub ShowMeterEntry()
    ThisWorkbook.Sheets(DATA_ENTRY_SHEET_NAME).Activate
    MsgBox "Professional meter reading entry interface activated.", vbInformation, "Meter Entry"
End Sub

Public Sub ManageComplexes()
    With ThisWorkbook.Sheets(COMPLEXES_SHEET_NAME)
        If .Visible <> xlSheetVisible Then .Visible = xlSheetVisible
        .Activate
    End With
    MsgBox "Complex management interface activated.", vbInformation, "Manage Complexes"
End Sub

Public Sub ManageTariffs()
    With ThisWorkbook.Sheets(TARIFF_STRUCTURES_NAME)
        If .Visible <> xlSheetVisible Then .Visible = xlSheetVisible
        .Activate
    End With
    MsgBox "Tariff management interface activated.", vbInformation, "Manage Tariffs"
End Sub

Public Sub GenerateBills()
    MsgBox "Professional bill generation interface.", vbInformation, "Generate Bills"
End Sub

Public Sub ViewReports()
    MsgBox "Professional reports and analytics interface.", vbInformation, "View Reports"
End Sub

Public Sub RefreshBillingDashboard()
    Call CreateProfessionalFinancialDashboard
    MsgBox "Professional billing dashboard refreshed.", vbInformation, "Dashboard Refreshed"
End Sub

Public Sub ApplyDashboardFilters()
    MsgBox "Dashboard filters applied.", vbInformation, "Filters Applied"
End Sub

Public Sub ShowMonthlyChart()
    MsgBox "Monthly consumption chart displayed.", vbInformation, "Monthly Chart"
End Sub

Public Sub ShowYearlyChart()
    MsgBox "Yearly consumption chart displayed.", vbInformation, "Yearly Chart"
End Sub

Public Sub ShowComplexChart()
    MsgBox "Complex comparison chart displayed.", vbInformation, "Complex Chart"
End Sub

'==================================================================================
'  DATA STRUCTURE SETUP (SIMPLIFIED FOR CORE FUNCTIONALITY)
'==================================================================================

Private Sub CreateBillingSystemSheets()
    Call CreateSheet(DASHBOARD_NAME)
    Call CreateSheet(DATABASE_NAME, xlSheetVeryHidden)
    Call CreateSheet(COMPLEXES_SHEET_NAME, xlSheetVeryHidden)
    Call CreateSheet(UNITS_SHEET_NAME, xlSheetVeryHidden)
    Call CreateSheet(TARIFF_STRUCTURES_NAME, xlSheetVeryHidden)
    Call CreateSheet(FIXED_CHARGES_NAME, xlSheetVeryHidden)
    Call CreateSheet(DATA_ENTRY_SHEET_NAME)
    Call CreateSheet(BILL_TEMPLATE_NAME)
End Sub

Private Sub SetupBillingDataStructures()
    Call SetupDatabaseSheet
    Call SetupComplexesSheet
    Call SetupUnitsSheet
    Call SetupTariffStructuresSheet
    Call SetupFixedChargesSheet
End Sub

Private Sub CreateSheet(sheetName As String, Optional visibility As XlSheetVisibility = xlSheetVisible)
    Dim ws As Worksheet
    
    Application.DisplayAlerts = False
    
    On Error Resume Next
    Set ws = ThisWorkbook.Sheets(sheetName)
    If Not ws Is Nothing Then
        ws.Visible = xlSheetVisible
        ws.Cells.Clear
        If ws.Shapes.Count > 0 Then
            Dim i As Long
            For i = ws.Shapes.Count To 1 Step -1
                ws.Shapes(i).Delete
            Next i
        End If
    Else
        Set ws = ThisWorkbook.Sheets.Add(After:=ThisWorkbook.Sheets(ThisWorkbook.Sheets.Count))
        ws.Name = sheetName
    End If
    On Error GoTo 0
    
    ws.Visible = visibility
    Application.DisplayAlerts = True
End Sub

Private Sub SetupDatabaseSheet()
    Dim ws As Worksheet: Set ws = ThisWorkbook.Sheets(DATABASE_NAME)
    
    Dim headers As Variant
    headers = Array("EntryID", "Timestamp", "ComplexName", "UnitName", "ReadingDate", _
                   "PreviousReading", "CurrentReading", "MechanicalConsumption", _
                   "DigitalConsumption", "BillingConsumption", "SubTotal", "VAT_Rate", _
                   "VAT_Amount", "TotalDue", "Status", "PrevReadingDate", "NumberOfMonths", _
                   "AvgMonthlyConsumption", "TariffBreakdown")
    
    With ws.Range("A1").Resize(1, UBound(headers) + 1)
        .Value = headers
        .Font.Bold = True
        .Interior.Color = DASHBOARD_PANEL_COLOR
        .Font.Color = DASHBOARD_TEXT_WHITE
    End With
    
    ws.Columns.AutoFit
End Sub

Private Sub SetupComplexesSheet()
    Dim ws As Worksheet: Set ws = ThisWorkbook.Sheets(COMPLEXES_SHEET_NAME)
    
    With ws.Range("A1:D1")
        .Value = Array("ComplexName", "ConsumptionTariff", "FixedCharge1", "FixedCharge2")
        .Font.Bold = True
    End With
    
    ws.Range("A2:D2").Value = Array("Sunset Villas", "Residential Water IBT", "Standard Basic Charge", "")
    ws.Range("A3:D3").Value = Array("Oakwood Manor", "Standard Water Flat Rate", "", "Security Levy")
    ws.Columns.AutoFit
End Sub

Private Sub SetupUnitsSheet()
    Dim ws As Worksheet: Set ws = ThisWorkbook.Sheets(UNITS_SHEET_NAME)
    
    With ws.Range("A1:B1")
        .Value = Array("ComplexName", "UnitName")
        .Font.Bold = True
    End With
    
    ws.Range("A2:B2").Value = Array("Sunset Villas", "Unit A1")
    ws.Range("A3:B3").Value = Array("Sunset Villas", "Unit A2")
    ws.Range("A4:B4").Value = Array("Oakwood Manor", "Unit 101")
    ws.Columns.AutoFit
End Sub

Private Sub SetupTariffStructuresSheet()
    Dim ws As Worksheet: Set ws = ThisWorkbook.Sheets(TARIFF_STRUCTURES_NAME)
    
    Dim headers As Variant
    headers = Array("ProfileName", "RateType", "FlatRate", "Block1_End", "Block1_Rate", _
                   "Block2_End", "Block2_Rate", "Block3_End", "Block3_Rate", _
                   "Block4_End", "Block4_Rate", "Block5_End", "Block5_Rate")
    
    ws.Range("A1").Resize(1, UBound(headers) + 1).Value = headers
    
    ' IBT structure
    ws.Range("A2").Resize(1, 13).Value = Array("Residential Water IBT", "IBT", "", _
                                              6, 11.97, 15, 30.11, 30, 34.49, 60, 43.27, 99999, 53.2)
    
    ' Flat rate
    ws.Range("A3").Resize(1, 3).Value = Array("Standard Water Flat Rate", "Flat", 33.456)
    ws.Columns.AutoFit
End Sub

Private Sub SetupFixedChargesSheet()
    Dim ws As Worksheet: Set ws = ThisWorkbook.Sheets(FIXED_CHARGES_NAME)
    
    ws.Range("A1:B1").Value = Array("ChargeName", "Amount")
    ws.Range("A2:B2").Value = Array("Standard Basic Charge", 47.52)
    ws.Range("A3:B3").Value = Array("Security Levy", 150)
    ws.Columns.AutoFit
End Sub

' Professional data entry interface (placeholder)
Private Sub CreateProfessionalDataEntry()
    Dim ws As Worksheet: Set ws = ThisWorkbook.Sheets(DATA_ENTRY_SHEET_NAME)
    
    ' Apply professional theme
    With ws.Cells.Interior
        .Color = DASHBOARD_DARK_BACKGROUND
        .Pattern = xlSolid
    End With
    
    ' Hide gridlines
    With Application.ActiveWindow
        .DisplayGridlines = False
        .DisplayHeadings = False
    End With
    
    ws.Range("A1").Value = "Professional Meter Reading Entry"
    ws.Range("A1").Font.Bold = True
    ws.Range("A1").Font.Size = 16
    ws.Range("A1").Font.Color = DASHBOARD_TEXT_WHITE
End Sub

' Professional bill template (placeholder)
Private Sub CreateProfessionalBillTemplate()
    Dim ws As Worksheet: Set ws = ThisWorkbook.Sheets(BILL_TEMPLATE_NAME)
    
    ' Apply professional theme
    With ws.Cells.Interior
        .Color = RGB(255, 255, 255) ' White background for bills
        .Pattern = xlSolid
    End With
    
    ws.Range("A1").Value = "Professional Water Utility Bill Template"
    ws.Range("A1").Font.Bold = True
    ws.Range("A1").Font.Size = 18
    ws.Range("A1").Font.Color = DASHBOARD_PANEL_COLOR
End Sub
