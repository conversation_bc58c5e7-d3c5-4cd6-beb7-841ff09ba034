' Specific DTPicker Code Debugging
' Use this to test your exact DTPicker code that's causing Runtime Error 1004

Option Explicit

' Test your specific DTPicker code with detailed error reporting
Public Sub TestYourDTPickerCode()
    Debug.Print "========================================="
    Debug.Print "TESTING YOUR SPECIFIC DTPICKER CODE"
    Debug.Print "Time: " & Now()
    Debug.Print "========================================="
    
    ' Enable detailed error handling
    On Error GoTo DetailedError
    
    Debug.Print "Step 1: Checking if DTPicker control exists..."
    
    ' Test if the DTPicker control exists on the active sheet
    Dim ws As Worksheet
    Set ws = ActiveSheet
    Debug.Print "Active Sheet: " & ws.Name
    
    ' Look for DTPicker controls
    Dim shp As Shape
    Dim dtPickerFound As Boolean
    dtPickerFound = False
    
    For Each shp In ws.Shapes
        Debug.Print "Found shape: " & shp.Name & " (Type: " & shp.Type & ")"
        If InStr(UCase(shp.Name), "DTPICKER") > 0 Then
            Debug.Print "*** DTPicker control found: " & shp.Name
            dtPickerFound = True
        End If
    Next shp
    
    If Not dtPickerFound Then
        Debug.Print "WARNING: No DTPicker control found on active sheet!"
        Debug.Print "This is likely the cause of your Runtime Error 1004"
    End If
    
    Debug.Print ""
    Debug.Print "Step 2: Testing DTPicker object access..."
    
    ' Try to access DTPicker1 (most common name)
    Dim dtpControl As Object
    Set dtpControl = Nothing
    
    ' Method 1: Try direct access
    On Error Resume Next
    Set dtpControl = ws.OLEObjects("DTPicker1").Object
    If Err.Number <> 0 Then
        Debug.Print "Cannot access DTPicker1 directly: " & Err.Description
        Err.Clear
    Else
        Debug.Print "SUCCESS: DTPicker1 accessed successfully"
    End If
    On Error GoTo DetailedError
    
    ' Method 2: Try alternative names
    If dtpControl Is Nothing Then
        Debug.Print "Trying alternative DTPicker names..."
        Dim commonNames As Variant
        commonNames = Array("DTPicker1", "DTPicker2", "DateTimePicker1", "dtpDate", "dtpStartDate", "dtpEndDate")
        
        Dim i As Integer
        For i = 0 To UBound(commonNames)
            On Error Resume Next
            Set dtpControl = ws.OLEObjects(commonNames(i)).Object
            If Err.Number = 0 Then
                Debug.Print "Found DTPicker with name: " & commonNames(i)
                Exit For
            End If
            Err.Clear
            On Error GoTo DetailedError
        Next i
    End If
    
    Debug.Print ""
    Debug.Print "Step 3: Testing With statement (your error location)..."
    
    If Not dtpControl Is Nothing Then
        Debug.Print "Attempting With statement on DTPicker..."
        With dtpControl
            Debug.Print "Current DTPicker value: " & .Value
            .Value = Date
            Debug.Print "Successfully set DTPicker value to: " & .Value
        End With
        Debug.Print "SUCCESS: With statement worked!"
    Else
        Debug.Print "CANNOT TEST: No DTPicker control available"
        Debug.Print "This confirms Runtime Error 1004 cause"
    End If
    
    Debug.Print ""
    Debug.Print "========================================="
    Debug.Print "DEBUGGING COMPLETE"
    Debug.Print "========================================="
    
    Exit Sub
    
DetailedError:
    Debug.Print ""
    Debug.Print "*** RUNTIME ERROR CAUGHT ***"
    Debug.Print "Error Number: " & Err.Number
    Debug.Print "Error Description: " & Err.Description
    Debug.Print "Error Source: " & Err.Source
    
    If Err.Number = 1004 Then
        Debug.Print ""
        Debug.Print "DIAGNOSIS: Runtime Error 1004 confirmed"
        Debug.Print "CAUSE: Application-defined or object-defined error"
        Debug.Print "LIKELY REASON: DTPicker control doesn't exist or isn't properly referenced"
        Debug.Print ""
        Debug.Print "SOLUTIONS:"
        Debug.Print "1. Add DTPicker control to your worksheet/form"
        Debug.Print "2. Check control name (might not be 'DTPicker1')"
        Debug.Print "3. Add Microsoft Date and Time Picker Control reference"
        Debug.Print "4. Use alternative date input method (see date_picker_fix.vba)"
    End If
End Sub

' Test with your exact code - PASTE YOUR DTPICKER CODE HERE
Public Sub TestYourExactCode()
    Debug.Print "========================================="
    Debug.Print "TESTING YOUR EXACT DTPICKER CODE"
    Debug.Print "========================================="
    
    On Error GoTo YourCodeError
    
    ' PASTE YOUR DTPICKER CODE BETWEEN THESE LINES:
    ' ============================================
    
    Debug.Print "Attempting your exact DTPicker code..."
    
    ' Example of what might be causing your error:
    ' With DTPicker1
    '     .Value = Date
    '     .Format = dtpShortDate
    ' End With
    
    Debug.Print "Please paste your actual DTPicker code in this subroutine"
    Debug.Print "Replace this message with your code that's causing the error"
    
    ' ============================================
    
    Debug.Print "Your code executed successfully!"
    Exit Sub
    
YourCodeError:
    Debug.Print ""
    Debug.Print "*** YOUR CODE CAUSED AN ERROR ***"
    Debug.Print "Error Number: " & Err.Number
    Debug.Print "Error Description: " & Err.Description
    Debug.Print "Line that failed: [Check the line highlighted in VBA Editor]"
    
    ' Provide specific guidance based on error
    Select Case Err.Number
        Case 1004
            Debug.Print ""
            Debug.Print "SOLUTION FOR ERROR 1004:"
            Debug.Print "- The DTPicker control doesn't exist"
            Debug.Print "- Check the control name in your code"
            Debug.Print "- Ensure the control is added to your form/sheet"
            
        Case 424
            Debug.Print ""
            Debug.Print "SOLUTION FOR ERROR 424:"
            Debug.Print "- Object required error"
            Debug.Print "- DTPicker reference is missing"
            Debug.Print "- Add Microsoft Date and Time Picker Control reference"
            
        Case Else
            Debug.Print ""
            Debug.Print "UNEXPECTED ERROR - Please check:"
            Debug.Print "- Control exists and is named correctly"
            Debug.Print "- Required references are added"
            Debug.Print "- Excel version compatibility"
    End Select
End Sub

' Quick test to see what controls are actually available
Public Sub ListAllControlsOnActiveSheet()
    Debug.Print "========================================="
    Debug.Print "ALL CONTROLS ON ACTIVE SHEET: " & ActiveSheet.Name
    Debug.Print "========================================="
    
    Dim ws As Worksheet
    Set ws = ActiveSheet
    
    ' List all OLE objects
    Debug.Print "OLE Objects:"
    Dim oleObj As OLEObject
    For Each oleObj In ws.OLEObjects
        Debug.Print "  - Name: " & oleObj.Name
        Debug.Print "    ProgID: " & oleObj.progID
        Debug.Print "    Type: " & TypeName(oleObj.Object)
        Debug.Print ""
    Next oleObj
    
    ' List all shapes
    Debug.Print "All Shapes:"
    Dim shp As Shape
    For Each shp In ws.Shapes
        Debug.Print "  - Name: " & shp.Name
        Debug.Print "    Type: " & shp.Type
        If shp.Type = msoOLEControlObject Then
            Debug.Print "    OLE ProgID: " & shp.OLEFormat.progID
        End If
        Debug.Print ""
    Next shp
    
    Debug.Print "========================================="
End Sub
