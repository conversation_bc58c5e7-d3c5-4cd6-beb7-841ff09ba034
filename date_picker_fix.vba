' Alternative to DTPicker - Compatible Date Input Solutions
' Use this instead of DTPicker to avoid Runtime Error 1004

' Method 1: Simple InputBox with Date Validation
Public Function GetDateInput(prompt As String, title As String) As Date
    Dim userInput As String
    Dim inputDate As Date
    
    Do
        userInput = InputBox(prompt & vbCrLf & "Format: MM/DD/YYYY or DD/MM/YYYY", title)
        
        If userInput = "" Then
            GetDateInput = 0 ' User cancelled
            Exit Function
        End If
        
        If IsDate(userInput) Then
            inputDate = CDate(userInput)
            GetDateInput = inputDate
            Exit Function
        Else
            MsgBox "Invalid date format. Please try again.", vbExclamation
        End If
    Loop
End Function

' Method 2: Cell-based Date Input with Calendar Popup
Public Sub SetupDateCell(ws As Worksheet, cellAddress As String)
    Dim cell As Range
    Set cell = ws.Range(cellAddress)
    
    ' Format cell for date
    cell.NumberFormat = "mm/dd/yyyy"
    cell.Value = Date ' Default to today
    
    ' Add validation
    With cell.Validation
        .Delete
        .Add Type:=xlValidateDate, AlertStyle:=xlValidAlertStop, _
             Formula1:="1/1/2020", Formula2:="12/31/2030"
        .InputTitle = "Date Entry"
        .InputMessage = "Enter a valid date (MM/DD/YYYY)"
        .ErrorTitle = "Invalid Date"
        .ErrorMessage = "Please enter a date between 2020 and 2030"
    End With
    
    ' Style the cell
    With cell
        .Interior.Color = RGB(255, 255, 255)
        .Borders.LineStyle = xlContinuous
        .Font.Bold = True
    End With
End Sub

' Method 3: UserForm with MonthView Control (if available)
' This is more compatible than DTPicker
Public Function ShowDatePicker() As Date
    ' Create a simple date selection form
    Dim result As Date
    
    ' You can create a UserForm with:
    ' - ComboBox for Month (1-12)
    ' - ComboBox for Day (1-31) 
    ' - ComboBox for Year (2020-2030)
    ' This avoids DTPicker entirely
    
    ShowDatePicker = result
End Function

' Method 4: Replace DTPicker usage in existing code
Public Sub FixDTPickerCode()
    ' Instead of: With DTPicker1
    '                .Value = Date
    '             End With
    
    ' Use this approach:
    Dim selectedDate As Date
    selectedDate = GetDateInput("Please enter the date:", "Date Selection")
    
    If selectedDate <> 0 Then
        ' Use the selected date
        Debug.Print "Selected date: " & selectedDate
        ' Apply to your billing system
    End If
End Sub

' Method 5: Quick Fix for existing DTPicker code
Public Sub ReplaceDTPickerWithCell()
    ' If you have existing code like:
    ' With DTPicker1
    '     .Value = Date
    ' End With
    
    ' Replace it with:
    Dim ws As Worksheet
    Set ws = ActiveSheet
    
    ' Setup a date input cell instead
    Call SetupDateCell(ws, "D13") ' or whatever cell you want to use
    
    MsgBox "Date picker replaced with cell-based input in D13", vbInformation
End Sub
