' Working Solutions for DTPicker Runtime Error 1004
' Fixes for "Me.OLEObjects("DTPicker1").Visible = False" error

Option Explicit

' SOLUTION 1: Error-Safe DTPicker Hide Method (RECOMMENDED)
Private Sub DTPicker1_LostFocus_SOLUTION1()
    ' This replaces your failing code with error-safe version
    On Error GoTo HideError
    
    ' Use ActiveSheet instead of Me to avoid context issues
    ActiveSheet.OLEObjects("DTPicker1").Visible = False
    Debug.Print "DTPicker1 hidden successfully using ActiveSheet method"
    Exit Sub
    
HideError:
    Debug.Print "Error hiding DTPicker1: " & Err.Description & " (Error " & Err.Number & ")"
    
    ' Try alternative methods if first fails
    On Error Resume Next
    
    ' Alternative 1: Try with ThisWorkbook.ActiveSheet
    ThisWorkbook.ActiveSheet.OLEObjects("DTPicker1").Visible = False
    If Err.Number = 0 Then
        Debug.Print "DTPicker1 hidden using ThisWorkbook.ActiveSheet method"
        Exit Sub
    End If
    Err.Clear
    
    ' Alternative 2: Try finding by iteration
    Dim oleObj As OLEObject
    For Each oleObj In ActiveSheet.OLEObjects
        If oleObj.Name = "DTPicker1" Then
            oleObj.Visible = False
            Debug.Print "DTPicker1 hidden using iteration method"
            Exit Sub
        End If
    Next oleObj
    
    Debug.Print "FAILED: Could not hide DTPicker1 with any method"
    On Error GoTo 0
End Sub

' SOLUTION 2: Robust Object Variable Method
Private Sub DTPicker1_LostFocus_SOLUTION2()
    ' Use object variable for safer access
    Dim dtpControl As OLEObject
    Dim ws As Worksheet
    
    Set ws = ActiveSheet ' or specify exact worksheet: Worksheets("YourSheetName")
    
    On Error Resume Next
    Set dtpControl = ws.OLEObjects("DTPicker1")
    
    If Err.Number <> 0 Then
        Debug.Print "Cannot find DTPicker1 control: " & Err.Description
        On Error GoTo 0
        Exit Sub
    End If
    
    If Not dtpControl Is Nothing Then
        dtpControl.Visible = False
        If Err.Number = 0 Then
            Debug.Print "DTPicker1 hidden successfully using object variable"
        Else
            Debug.Print "Error setting Visible property: " & Err.Description
        End If
    End If
    
    On Error GoTo 0
End Sub

' SOLUTION 3: Context-Aware Method (Handles Worksheet vs UserForm)
Private Sub DTPicker1_LostFocus_SOLUTION3()
    On Error GoTo ContextError
    
    ' Determine the correct context and use appropriate method
    If TypeName(Me) = "Worksheet" Then
        ' We're in a worksheet module - use Me.OLEObjects
        Me.OLEObjects("DTPicker1").Visible = False
        Debug.Print "DTPicker1 hidden using Me.OLEObjects (Worksheet context)"
        
    ElseIf TypeName(Me) = "UserForm" Then
        ' We're in a UserForm - use Me.Controls
        Me.Controls("DTPicker1").Visible = False
        Debug.Print "DTPicker1 hidden using Me.Controls (UserForm context)"
        
    Else
        ' We're in a standard module or class - use ActiveSheet
        ActiveSheet.OLEObjects("DTPicker1").Visible = False
        Debug.Print "DTPicker1 hidden using ActiveSheet (Standard module context)"
    End If
    
    Exit Sub
    
ContextError:
    Debug.Print "Context-aware method failed: " & Err.Description
    Debug.Print "Current context: " & TypeName(Me)
    
    ' Fallback to ActiveSheet method
    On Error Resume Next
    ActiveSheet.OLEObjects("DTPicker1").Visible = False
    If Err.Number = 0 Then
        Debug.Print "Fallback method succeeded"
    Else
        Debug.Print "All methods failed: " & Err.Description
    End If
    On Error GoTo 0
End Sub

' SOLUTION 4: Universal DTPicker Hide Function
Public Sub HideDTPicker(Optional controlName As String = "DTPicker1", Optional targetSheet As Worksheet = Nothing)
    ' Universal function that can be called from anywhere
    Dim ws As Worksheet
    Dim dtpControl As OLEObject
    
    ' Determine target worksheet
    If targetSheet Is Nothing Then
        Set ws = ActiveSheet
    Else
        Set ws = targetSheet
    End If
    
    Debug.Print "Attempting to hide " & controlName & " on sheet: " & ws.Name
    
    On Error Resume Next
    
    ' Method 1: Direct OLEObjects access
    Set dtpControl = ws.OLEObjects(controlName)
    If Err.Number = 0 And Not dtpControl Is Nothing Then
        dtpControl.Visible = False
        If Err.Number = 0 Then
            Debug.Print "SUCCESS: " & controlName & " hidden using direct access"
            Exit Sub
        End If
    End If
    Err.Clear
    
    ' Method 2: Search by iteration (in case name is slightly different)
    For Each dtpControl In ws.OLEObjects
        If InStr(UCase(dtpControl.Name), UCase(controlName)) > 0 Then
            dtpControl.Visible = False
            If Err.Number = 0 Then
                Debug.Print "SUCCESS: " & dtpControl.Name & " hidden using search method"
                Exit Sub
            End If
        End If
    Next dtpControl
    
    Debug.Print "FAILED: Could not find or hide " & controlName
    On Error GoTo 0
End Sub

' Your corrected event handler - USE THIS ONE
Private Sub DTPicker1_LostFocus()
    ' CORRECTED VERSION - Replace your failing code with this
    Call HideDTPicker("DTPicker1")
End Sub

' Test all solutions to see which works in your environment
Public Sub TestAllDTPickerSolutions()
    Debug.Print "========================================="
    Debug.Print "TESTING ALL DTPICKER HIDE SOLUTIONS"
    Debug.Print "Time: " & Now()
    Debug.Print "========================================="
    
    ' First, make DTPicker visible for testing
    On Error Resume Next
    ActiveSheet.OLEObjects("DTPicker1").Visible = True
    Debug.Print "Made DTPicker1 visible for testing"
    On Error GoTo 0
    
    ' Test Solution 1
    Debug.Print ""
    Debug.Print "--- Testing Solution 1: ActiveSheet Method ---"
    Call DTPicker1_LostFocus_SOLUTION1
    
    ' Make visible again
    On Error Resume Next
    ActiveSheet.OLEObjects("DTPicker1").Visible = True
    On Error GoTo 0
    
    ' Test Solution 2
    Debug.Print ""
    Debug.Print "--- Testing Solution 2: Object Variable Method ---"
    Call DTPicker1_LostFocus_SOLUTION2
    
    ' Make visible again
    On Error Resume Next
    ActiveSheet.OLEObjects("DTPicker1").Visible = True
    On Error GoTo 0
    
    ' Test Solution 3
    Debug.Print ""
    Debug.Print "--- Testing Solution 3: Context-Aware Method ---"
    Call DTPicker1_LostFocus_SOLUTION3
    
    ' Make visible again
    On Error Resume Next
    ActiveSheet.OLEObjects("DTPicker1").Visible = True
    On Error GoTo 0
    
    ' Test Solution 4
    Debug.Print ""
    Debug.Print "--- Testing Solution 4: Universal Function ---"
    Call HideDTPicker("DTPicker1")
    
    Debug.Print ""
    Debug.Print "========================================="
    Debug.Print "ALL SOLUTIONS TESTED - Check results above"
    Debug.Print "Use the solution that showed 'SUCCESS' in output"
    Debug.Print "========================================="
End Sub

' Diagnostic function to understand your specific context
Public Sub DiagnoseYourContext()
    Debug.Print "========================================="
    Debug.Print "DIAGNOSING YOUR SPECIFIC CONTEXT"
    Debug.Print "========================================="
    
    On Error Resume Next
    
    Debug.Print "Current context analysis:"
    Debug.Print "- TypeName(Me): " & TypeName(Me)
    Debug.Print "- ActiveSheet.Name: " & ActiveSheet.Name
    Debug.Print "- Me.Name: " & Me.Name
    
    ' Check if Me has OLEObjects collection
    Dim oleCount As Long
    oleCount = Me.OLEObjects.Count
    If Err.Number = 0 Then
        Debug.Print "- Me.OLEObjects.Count: " & oleCount
        Debug.Print "- Me.OLEObjects collection: AVAILABLE"
    Else
        Debug.Print "- Me.OLEObjects collection: NOT AVAILABLE (" & Err.Description & ")"
    End If
    Err.Clear
    
    ' Check ActiveSheet OLEObjects
    oleCount = ActiveSheet.OLEObjects.Count
    If Err.Number = 0 Then
        Debug.Print "- ActiveSheet.OLEObjects.Count: " & oleCount
    Else
        Debug.Print "- ActiveSheet.OLEObjects: ERROR (" & Err.Description & ")"
    End If
    
    Debug.Print ""
    Debug.Print "RECOMMENDATION:"
    If TypeName(Me) = "Worksheet" Then
        Debug.Print "Use: Me.OLEObjects(""DTPicker1"").Visible = False"
    Else
        Debug.Print "Use: ActiveSheet.OLEObjects(""DTPicker1"").Visible = False"
    End If
    
    On Error GoTo 0
End Sub
