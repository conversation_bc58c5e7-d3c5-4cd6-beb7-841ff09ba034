' DTPicker "Me" Keyword Runtime Error 1004 Debugging
' Specifically for: Me.OLEObjects("DTPicker1").Visible = False
' This will output detailed diagnostics to the Immediate Window

Option Explicit

' Main debugging routine for the specific "Me" keyword issue
Public Sub DebugMeOLEObjectsError()
    Debug.Print "========================================="
    Debug.Print "DEBUGGING Me.OLEObjects DTPicker ERROR"
    Debug.Print "Time: " & Now()
    Debug.Print "========================================="
    
    ' Step 1: Determine what "Me" refers to in current context
    Call CheckMeContext
    
    ' Step 2: Test OLEObjects collection access
    Call TestOLEObjectsAccess
    
    ' Step 3: Find DTPicker1 control
    Call FindDTPicker1Control
    
    ' Step 4: Test different access methods
    Call TestDifferentAccessMethods
    
    ' Step 5: Provide working solutions
    Call ShowWorkingSolutions
    
    Debug.Print "========================================="
    Debug.Print "DEBUGGING COMPLETE - Check solutions above"
    Debug.Print "========================================="
End Sub

' Check what "Me" refers to in different contexts
Private Sub CheckMeContext()
    Debug.Print ""
    Debug.Print "--- CHECKING 'ME' KEYWORD CONTEXT ---"
    
    On Error Resume Next
    
    ' Test if Me is a Worksheet
    If TypeName(Me) = "Worksheet" Then
        Debug.Print "SUCCESS: Me refers to Worksheet: " & Me.Name
        Debug.Print "Me.OLEObjects should be valid for Worksheet"
    ElseIf TypeName(Me) = "UserForm" Then
        Debug.Print "SUCCESS: Me refers to UserForm: " & Me.Name
        Debug.Print "WARNING: UserForms use Me.Controls, not Me.OLEObjects"
    ElseIf TypeName(Me) = "Class" Then
        Debug.Print "ERROR: Me refers to Class module"
        Debug.Print "PROBLEM: Classes don't have OLEObjects collection"
    Else
        Debug.Print "UNKNOWN: Me refers to: " & TypeName(Me)
    End If
    
    ' Test if OLEObjects collection exists
    Dim oleCount As Long
    oleCount = -1
    oleCount = Me.OLEObjects.Count
    
    If Err.Number <> 0 Then
        Debug.Print "ERROR: Me.OLEObjects is not valid in this context"
        Debug.Print "Error: " & Err.Description
        Debug.Print "CAUSE: Wrong object type or context"
    Else
        Debug.Print "SUCCESS: Me.OLEObjects collection exists with " & oleCount & " objects"
    End If
    
    Err.Clear
    On Error GoTo 0
End Sub

' Test OLEObjects collection access methods
Private Sub TestOLEObjectsAccess()
    Debug.Print ""
    Debug.Print "--- TESTING OLEOBJECTS ACCESS METHODS ---"
    
    On Error Resume Next
    
    ' Method 1: Direct Me.OLEObjects access
    Debug.Print "Method 1: Testing Me.OLEObjects(""DTPicker1"")"
    Dim obj1 As Object
    Set obj1 = Me.OLEObjects("DTPicker1")
    
    If Err.Number <> 0 Then
        Debug.Print "FAILED: " & Err.Description & " (Error " & Err.Number & ")"
    Else
        Debug.Print "SUCCESS: Me.OLEObjects(""DTPicker1"") works"
    End If
    Err.Clear
    
    ' Method 2: ActiveSheet.OLEObjects access
    Debug.Print "Method 2: Testing ActiveSheet.OLEObjects(""DTPicker1"")"
    Dim obj2 As Object
    Set obj2 = ActiveSheet.OLEObjects("DTPicker1")
    
    If Err.Number <> 0 Then
        Debug.Print "FAILED: " & Err.Description & " (Error " & Err.Number & ")"
    Else
        Debug.Print "SUCCESS: ActiveSheet.OLEObjects(""DTPicker1"") works"
    End If
    Err.Clear
    
    ' Method 3: ThisWorkbook.ActiveSheet.OLEObjects access
    Debug.Print "Method 3: Testing ThisWorkbook.ActiveSheet.OLEObjects(""DTPicker1"")"
    Dim obj3 As Object
    Set obj3 = ThisWorkbook.ActiveSheet.OLEObjects("DTPicker1")
    
    If Err.Number <> 0 Then
        Debug.Print "FAILED: " & Err.Description & " (Error " & Err.Number & ")"
    Else
        Debug.Print "SUCCESS: ThisWorkbook.ActiveSheet.OLEObjects(""DTPicker1"") works"
    End If
    Err.Clear
    
    On Error GoTo 0
End Sub

' Find the DTPicker1 control using different methods
Private Sub FindDTPicker1Control()
    Debug.Print ""
    Debug.Print "--- SEARCHING FOR DTPICKER1 CONTROL ---"
    
    ' Search in current worksheet
    Dim ws As Worksheet
    Set ws = ActiveSheet
    Debug.Print "Searching in worksheet: " & ws.Name
    
    ' List all OLE objects
    Debug.Print "All OLE Objects found:"
    Dim oleObj As OLEObject
    Dim foundDTPicker As Boolean
    foundDTPicker = False
    
    On Error Resume Next
    For Each oleObj In ws.OLEObjects
        Debug.Print "  - Name: " & oleObj.Name & " | ProgID: " & oleObj.progID
        If oleObj.Name = "DTPicker1" Then
            Debug.Print "    *** FOUND DTPicker1! ***"
            Debug.Print "    Visible: " & oleObj.Visible
            Debug.Print "    Object Type: " & TypeName(oleObj.Object)
            foundDTPicker = True
        End If
    Next oleObj
    On Error GoTo 0
    
    If Not foundDTPicker Then
        Debug.Print "WARNING: DTPicker1 control not found!"
        Debug.Print "CAUSE: Control doesn't exist or has different name"
    End If
    
    ' Also check for similar names
    Debug.Print ""
    Debug.Print "Checking for similar control names:"
    For Each oleObj In ws.OLEObjects
        If InStr(UCase(oleObj.Name), "DTPICKER") > 0 Or InStr(UCase(oleObj.Name), "DATE") > 0 Then
            Debug.Print "  - Similar control found: " & oleObj.Name
        End If
    Next oleObj
End Sub

' Test different methods to access and hide the control
Private Sub TestDifferentAccessMethods()
    Debug.Print ""
    Debug.Print "--- TESTING DIFFERENT ACCESS METHODS ---"
    
    On Error Resume Next
    
    ' Test Method 1: Me.OLEObjects (your current failing method)
    Debug.Print "Testing Method 1: Me.OLEObjects(""DTPicker1"").Visible = False"
    Me.OLEObjects("DTPicker1").Visible = False
    If Err.Number <> 0 Then
        Debug.Print "FAILED: " & Err.Description & " (Error " & Err.Number & ")"
        If Err.Number = 1004 Then
            Debug.Print "DIAGNOSIS: Runtime Error 1004 - Invalid use of Me or object not found"
        End If
    Else
        Debug.Print "SUCCESS: Method 1 works!"
    End If
    Err.Clear
    
    ' Test Method 2: ActiveSheet.OLEObjects
    Debug.Print "Testing Method 2: ActiveSheet.OLEObjects(""DTPicker1"").Visible = False"
    ActiveSheet.OLEObjects("DTPicker1").Visible = False
    If Err.Number <> 0 Then
        Debug.Print "FAILED: " & Err.Description & " (Error " & Err.Number & ")"
    Else
        Debug.Print "SUCCESS: Method 2 works!"
    End If
    Err.Clear
    
    ' Test Method 3: Direct object reference
    Debug.Print "Testing Method 3: Direct object variable"
    Dim dtpControl As OLEObject
    Set dtpControl = ActiveSheet.OLEObjects("DTPicker1")
    If Err.Number = 0 Then
        dtpControl.Visible = False
        If Err.Number = 0 Then
            Debug.Print "SUCCESS: Method 3 works!"
        Else
            Debug.Print "FAILED: " & Err.Description
        End If
    Else
        Debug.Print "FAILED: Cannot get object reference - " & Err.Description
    End If
    Err.Clear
    
    On Error GoTo 0
End Sub

' Show working solutions for the DTPicker1_LostFocus event
Private Sub ShowWorkingSolutions()
    Debug.Print ""
    Debug.Print "--- WORKING SOLUTIONS FOR YOUR CODE ---"
    Debug.Print ""
    Debug.Print "SOLUTION 1: Use ActiveSheet instead of Me"
    Debug.Print "Private Sub DTPicker1_LostFocus()"
    Debug.Print "    On Error Resume Next"
    Debug.Print "    ActiveSheet.OLEObjects(""DTPicker1"").Visible = False"
    Debug.Print "    If Err.Number <> 0 Then"
    Debug.Print "        Debug.Print ""Error hiding DTPicker: "" & Err.Description"
    Debug.Print "    End If"
    Debug.Print "    On Error GoTo 0"
    Debug.Print "End Sub"
    Debug.Print ""
    
    Debug.Print "SOLUTION 2: Use error-safe object variable"
    Debug.Print "Private Sub DTPicker1_LostFocus()"
    Debug.Print "    Dim dtpControl As OLEObject"
    Debug.Print "    On Error Resume Next"
    Debug.Print "    Set dtpControl = ActiveSheet.OLEObjects(""DTPicker1"")"
    Debug.Print "    If Not dtpControl Is Nothing Then"
    Debug.Print "        dtpControl.Visible = False"
    Debug.Print "    End If"
    Debug.Print "    On Error GoTo 0"
    Debug.Print "End Sub"
    Debug.Print ""
    
    Debug.Print "SOLUTION 3: Use worksheet-specific reference"
    Debug.Print "Private Sub DTPicker1_LostFocus()"
    Debug.Print "    Dim ws As Worksheet"
    Debug.Print "    Set ws = ThisWorkbook.ActiveSheet"
    Debug.Print "    On Error Resume Next"
    Debug.Print "    ws.OLEObjects(""DTPicker1"").Visible = False"
    Debug.Print "    On Error GoTo 0"
    Debug.Print "End Sub"
End Sub
