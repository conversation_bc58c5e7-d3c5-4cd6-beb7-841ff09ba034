STEP 2: BILLING CALCULATION ENGINE UPGRADE
==================================================

Copy this code and add to your working VBA module:


'==================================================================================
'  STEP 2: BILLING CALCULATION ENGINE UPGRADE
'==================================================================================

' Add this to get complete billing calculations

' Type definition for billing results
Public Type BillCalculationResult
    subTotal As Double
    vatAmount As Double
    totalDue As Double
    totalFixedCharges As Double
    billConsumption As Double
    MechConsumption As Double
    numberOfMonths As Long
    AverageMonthlyConsumption As Double
    TariffBreakdown As String
End Type

Public Sub SetupTariffAndCharges()
    ' Create hidden sheets for tariff structures and fixed charges
    On Error GoTo ErrorHandler
    
    ' Create Tariff Structures sheet
    Dim tariffWs As Worksheet
    On Error Resume Next
    Set tariffWs = ThisWorkbook.Sheets("Tariff_Structures")
    On Error GoTo 0
    
    If tariffWs Is Nothing Then
        Set tariffWs = ThisWorkbook.Sheets.Add
        tariffWs.Name = "Tariff_Structures"
        tariffWs.Visible = xlSheetVeryHidden
        
        ' Setup headers
        Dim headers As Variant
        headers = Array("ProfileName", "RateType", "FlatRate", "Block1_End", "Block1_Rate", "Block2_End", "Block2_Rate", "Block3_End", "Block3_Rate", "Block4_End", "Block4_Rate", "Block5_End", "Block5_Rate")
        tariffWs.Range("A1").Resize(1, UBound(headers) + 1).Value = headers
        
        ' Add sample tariff profiles
        tariffWs.Range("A2").Resize(1, 12).Value = Array("Residential Water IBT", "IBT", "", 6, 11.97, 15, 30.11, 30, 34.49, 60, 43.27, 99999, 53.2)
        tariffWs.Range("A3").Resize(1, 3).Value = Array("Standard Water Flat Rate", "Flat", 33.456)
    End If
    
    ' Create Fixed Charges sheet
    Dim fixedWs As Worksheet
    On Error Resume Next
    Set fixedWs = ThisWorkbook.Sheets("Fixed_Charges")
    On Error GoTo 0
    
    If fixedWs Is Nothing Then
        Set fixedWs = ThisWorkbook.Sheets.Add
        fixedWs.Name = "Fixed_Charges"
        fixedWs.Visible = xlSheetVeryHidden
        
        fixedWs.Range("A1:B1").Value = Array("ChargeName", "Amount")
        fixedWs.Range("A2:B2").Value = Array("Standard Basic Charge", 47.52)
        fixedWs.Range("A3:B3").Value = Array("Security Levy", 150)
    End If
    
    ' Update Complexes sheet with tariff links
    Dim complexWs As Worksheet: Set complexWs = ThisWorkbook.Sheets("Complexes")
    
    ' Add headers if not exist
    If complexWs.Range("B1").Value = "" Then
        complexWs.Range("A1:D1").Value = Array("ComplexName", "ConsumptionTariff", "FixedCharge1", "FixedCharge2")
        complexWs.Range("A1:D1").Font.Bold = True
        
        ' Add sample data with tariff links
        complexWs.Range("A2:D2").Value = Array("Sunset Villas", "Residential Water IBT", "Standard Basic Charge", "")
        complexWs.Range("A3:D3").Value = Array("Oakwood Manor", "Standard Water Flat Rate", "", "Security Levy")
    End If
    
    MsgBox "✅ Tariff and charges setup complete!" & vbCrLf & _
           "• IBT tariff structures created" & vbCrLf & _
           "• Fixed charges defined" & vbCrLf & _
           "• Complex tariff links established", vbInformation, "Setup Complete"
    Exit Sub
    
ErrorHandler:
    MsgBox "Error setting up tariffs: " & Err.Description, vbCritical
End Sub

Private Function CalculateBillValues(ByVal prevReading As Double, ByVal currReading As Double, ByVal digitalConsumption As Double, ByVal prevDate As Date, ByVal currDate As Date, ByVal complexName As String) As BillCalculationResult
    Dim Result As BillCalculationResult
    
    ' Step 1: Time calculation
    Result.numberOfMonths = DateDiff("m", prevDate, currDate)
    If Result.numberOfMonths < 1 Then Result.numberOfMonths = 1
    
    ' Step 2: Consumption calculation
    Result.MechConsumption = Abs(currReading - prevReading)
    Result.billConsumption = Result.MechConsumption - digitalConsumption
    If Result.billConsumption < 0 Then
        CalculateBillValues = Result
        Exit Function
    End If
    
    ' Step 3: Average calculation
    Result.AverageMonthlyConsumption = Result.billConsumption / Result.numberOfMonths
    
    ' Step 4: Get complex info
    Dim compWs As Worksheet: Set compWs = ThisWorkbook.Sheets("Complexes")
    Dim compRow As Range: Set compRow = compWs.Columns("A").Find(complexName, LookIn:=xlValues, LookAt:=xlWhole)
    If compRow Is Nothing Then Exit Function
    
    ' Step 5: Calculate fixed charges
    Dim fixedWs As Worksheet: Set fixedWs = ThisWorkbook.Sheets("Fixed_Charges")
    Dim fixedCharge1Name As String: fixedCharge1Name = compRow.Offset(0, 2).Value
    Dim fixedCharge2Name As String: fixedCharge2Name = compRow.Offset(0, 3).Value
    Dim fixedCharge1 As Double, fixedCharge2 As Double
    
    If fixedCharge1Name <> "" Then
        Dim fc1Row As Range: Set fc1Row = fixedWs.Columns("A").Find(fixedCharge1Name, LookIn:=xlValues, LookAt:=xlWhole)
        If Not fc1Row Is Nothing Then fixedCharge1 = fc1Row.Offset(0, 1).Value
    End If
    If fixedCharge2Name <> "" Then
        Dim fc2Row As Range: Set fc2Row = fixedWs.Columns("A").Find(fixedCharge2Name, LookIn:=xlValues, LookAt:=xlWhole)
        If Not fc2Row Is Nothing Then fixedCharge2 = fc2Row.Offset(0, 1).Value
    End If
    Result.totalFixedCharges = (fixedCharge1 + fixedCharge2) * Result.numberOfMonths
    
    ' Step 6: Calculate consumption charges
    Dim tariffWs As Worksheet: Set tariffWs = ThisWorkbook.Sheets("Tariff_Structures")
    Dim tariffName As String: tariffName = compRow.Offset(0, 1).Value
    Dim tariffRow As Range: Set tariffRow = tariffWs.Columns("A").Find(tariffName, LookIn:=xlValues, LookAt:=xlWhole)
    If tariffRow Is Nothing Then Exit Function
    
    Dim rateType As String: rateType = tariffRow.Offset(0, 1).Value
    Dim TotalConsumptionCharges As Double
    Dim tariffBreakdownString As String
    
    If rateType = "Flat" Then
        Dim flatRate As Double: flatRate = tariffRow.Offset(0, 2).Value
        TotalConsumptionCharges = Result.billConsumption * flatRate
        tariffBreakdownString = "Flat Rate: " & Result.billConsumption & " x " & FormatCurrency(flatRate, 2)
    ElseIf rateType = "IBT" Then
        TotalConsumptionCharges = CalculateIBT(Result.billConsumption, tariffRow)
        tariffBreakdownString = BuildIBTBreakdownString(Result.billConsumption, tariffRow)
    End If
    Result.TariffBreakdown = tariffBreakdownString
    
    ' Step 7: Final calculation
    Result.subTotal = TotalConsumptionCharges + Result.totalFixedCharges
    Dim vatRate As Double: vatRate = 0.15
    Result.vatAmount = Result.subTotal * vatRate
    Result.totalDue = Result.subTotal + Result.vatAmount
    CalculateBillValues = Result
End Function

Private Function CalculateIBT(consumption As Double, profileRow As Range) As Double
    Dim totalCost As Double, i As Long, blockEnd As Double, blockRate As Double, prevEnd As Double, used As Double
    totalCost = 0: prevEnd = 0
    For i = 1 To 5
        blockEnd = profileRow.Offset(0, 2 + (i - 1) * 2).Value
        blockRate = profileRow.Offset(0, 3 + (i - 1) * 2).Value
        If consumption > prevEnd Then
            used = Application.Min(consumption, blockEnd) - prevEnd
            totalCost = totalCost + used * blockRate
            prevEnd = blockEnd
        Else
            Exit For
        End If
    Next i
    CalculateIBT = totalCost
End Function

Private Function BuildIBTBreakdownString(consumption As Double, profileRow As Range) As String
    Dim breakdown As String, i As Long, blockEnd As Double, blockRate As Double, prevEnd As Double, used As Double, blockCost As Double
    prevEnd = 0: breakdown = ""
    For i = 1 To 5
        blockEnd = profileRow.Offset(0, 2 + (i - 1) * 2).Value
        blockRate = profileRow.Offset(0, 3 + (i - 1) * 2).Value
        If consumption > prevEnd Then
            used = Application.Min(consumption, blockEnd) - prevEnd
            If used > 0 Then
                blockCost = used * blockRate
                breakdown = breakdown & "Block " & i & ": " & Format(used, "0.00") & " x " & FormatCurrency(blockRate, 2) & " = " & FormatCurrency(blockCost, 2) & vbCrLf
            End If
            prevEnd = blockEnd
        Else
            Exit For
        End If
    Next i
    BuildIBTBreakdownString = Left(breakdown, Len(breakdown) - 2)
End Function

Public Sub TestStep2()
    MsgBox "✅ STEP 2 UPGRADE COMPLETE!" & vbCrLf & _
           "Billing Calculation Engine Added:" & vbCrLf & _
           "• IBT (Increasing Block Tariff) calculations" & vbCrLf & _
           "• Fixed charge management" & vbCrLf & _
           "• VAT calculations" & vbCrLf & _
           "• Professional billing workflow", vbInformation, "Step 2 Complete"
End Sub
