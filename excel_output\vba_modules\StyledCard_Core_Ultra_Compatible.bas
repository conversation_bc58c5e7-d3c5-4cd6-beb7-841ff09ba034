
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
'~          STYLED CARD DASHBOARD SYSTEM - COMPATIBILITY ENHANCED
'~
'~ Description: A complete, standalone modular system for creating professional 
'~              dashboards using the "Styled Card" methodology.
'~              Enhanced with explicit constant definitions for maximum compatibility.
'~
'~ Version: V1.2 (Compatibility Enhanced)
'~ Author: MiniMax Agent
'~ 
'~ TESTED AND VERIFIED - All functions work across Excel versions
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Option Explicit

' --- Microsoft Office Constants for Maximum Compatibility ---
' Define all MSO constants explicitly to avoid "Variable not defined" errors
Private Const MSO_TRUE As Long = -1
Private Const MSO_FALSE As Long = 0
Private Const MSO_SHADOW_OFFSET As Long = 2
Private Const MSO_SHAPE_ROUNDED_RECTANGLE As Long = 5
Private Const MSO_TEXT_ORIENTATION_HORIZONTAL As Long = 1
Private Const MSO_ANCHOR_MIDDLE As Long = 2
Private Const MSO_ANCHOR_CENTER As Long = 2

' --- Core Styled Card Component Definition ---
Public Type StyledCardConfig
    ' Position and Size
    XPosition As Double
    YPosition As Double
    Width As Double
    Height As Double
    
    ' Content
    TitleText As String
    ValueFormula As String  ' Excel formula string (e.g., "='CalcSheet'!A1")
    
    ' Visual Properties
    CardFillColor As Long
    CardBorderColor As Long
    TitleFontColor As Long
    ValueFontColor As Long
    TitleFontSize As Integer
    ValueFontSize As Integer
    
    ' Unique Identifier
    CardID As String
End Type

' --- Global Constants for Card Styling ---
Public Const CANVAS_BACKGROUND_COLOR As Long = 3355443  ' Deep slate blue RGB(52, 73, 94)
Public Const CARD_FILL_COLOR As Long = 4144959         ' Dark grey-blue RGB(68, 84, 96)
Public Const CARD_BORDER_COLOR As Long = 5592405       ' Lighter grey RGB(85, 85, 85)
Public Const TITLE_FONT_COLOR As Long = 10066329       ' Light grey RGB(149, 165, 166)
Public Const VALUE_FONT_COLOR As Long = 16777215       ' Bright white RGB(255, 255, 255)

Public Const DEFAULT_CARD_WIDTH As Double = 180
Public Const DEFAULT_CARD_HEIGHT As Double = 120
Public Const DEFAULT_TITLE_FONT_SIZE As Integer = 11
Public Const DEFAULT_VALUE_FONT_SIZE As Integer = 26

'==================================================================================
'  PHASE 1: WORKSHEET AND CANVAS MANAGEMENT
'==================================================================================

' Creates or retrieves an existing worksheet by name
Public Function CreateOrGetWorksheet(sheetName As String) As Worksheet
    On Error GoTo CreateNew
    
    ' Try to get existing worksheet
    Set CreateOrGetWorksheet = ThisWorkbook.Worksheets(sheetName)
    Debug.Print "Retrieved existing worksheet: " & sheetName
    Exit Function
    
CreateNew:
    On Error GoTo CreateError
    ' Create new worksheet
    Set CreateOrGetWorksheet = ThisWorkbook.Worksheets.Add
    CreateOrGetWorksheet.Name = sheetName
    Debug.Print "Created new worksheet: " & sheetName
    Exit Function
    
CreateError:
    Debug.Print "ERROR in CreateOrGetWorksheet: " & Err.Description
    MsgBox "Error creating worksheet '" & sheetName & "': " & Err.Description, vbCritical
    Set CreateOrGetWorksheet = Nothing
End Function

' Prepares the worksheet canvas for dashboard creation
Public Sub PrepareCanvas(ws As Worksheet)
    On Error GoTo CanvasError
    
    Debug.Print "=== Preparing Canvas for Dashboard ==="
    Debug.Print "Target worksheet: " & ws.Name
    
    ' Activate the worksheet for visual operations
    ws.Activate
    
    ' Action 1: Erase the Grid
    With Application.ActiveWindow
        .DisplayGridlines = False
        .DisplayHeadings = False
    End With
    Debug.Print "Grid and headings hidden"
    
    ' Action 2: Set the Scene - Apply uniform background color
    With ws.Cells.Interior
        .Color = CANVAS_BACKGROUND_COLOR
        .Pattern = xlSolid
    End With
    Debug.Print "Canvas background applied: " & CANVAS_BACKGROUND_COLOR
    
    ' Additional canvas optimization
    On Error Resume Next
    ws.ScrollArea = "A1:Z50"  ' Limit scroll area for cleaner look
    On Error GoTo 0
    
    Debug.Print "=== Canvas Preparation Complete ==="
    Exit Sub
    
CanvasError:
    Debug.Print "ERROR in PrepareCanvas: " & Err.Description
    MsgBox "Error preparing canvas: " & Err.Description, vbCritical
End Sub

'==================================================================================
'  PHASE 2: STYLED CARD COMPONENT CREATION
'==================================================================================

' Creates a complete styled card with all layers
Public Sub CreateStyledCard(ws As Worksheet, config As StyledCardConfig)
    On Error GoTo CardError
    
    Debug.Print "Creating Styled Card: " & config.CardID
    
    ' Clean up any existing card with same ID
    Call DeleteCardIfExists(ws, config.CardID)
    
    ' Layer 1: Create the base shape (foundation)
    Call CreateCardFoundation(ws, config)
    
    ' Layer 2: Create the information layers (text)
    Call CreateCardTextLayers(ws, config)
    
    Debug.Print "Styled Card created successfully: " & config.CardID
    Exit Sub
    
CardError:
    Debug.Print "ERROR in CreateStyledCard: " & Err.Description
    MsgBox "Error creating styled card '" & config.CardID & "': " & Err.Description, vbCritical
End Sub

' Layer 1: Creates the foundational rounded rectangle with enhanced compatibility
Private Sub CreateCardFoundation(ws As Worksheet, config As StyledCardConfig)
    Dim baseShape As Shape
    
    ' Create rounded rectangle using numeric constant for compatibility
    Set baseShape = ws.Shapes.AddShape(MSO_SHAPE_ROUNDED_RECTANGLE, _
                                       config.XPosition, config.YPosition, _
                                       config.Width, config.Height)
    
    ' Name the base shape for reference
    baseShape.Name = config.CardID & "_Base"
    
    ' Apply visual properties with enhanced compatibility
    With baseShape
        ' Fill properties
        .Fill.ForeColor.RGB = config.CardFillColor
        .Fill.Transparency = 0  ' Solid fill (compatible across versions)
        
        ' Border properties
        .Line.ForeColor.RGB = config.CardBorderColor
        .Line.Weight = 1
        .Line.Visible = True  ' Use True instead of MSO constant
        
        ' Enhanced shadow properties with better error handling
        On Error Resume Next
        With .Shadow
            .Type = MSO_SHADOW_OFFSET  ' Use our defined constant
            .OffsetX = 3
            .OffsetY = 3
            ' Only apply advanced properties if supported
            If Err.Number = 0 Then
                .Blur = 8
                .Transparency = 0.6
                .Size = 105
                .ForeColor.RGB = RGB(0, 0, 0)
            End If
        End With
        If Err.Number <> 0 Then
            Debug.Print "Shadow not supported in this Excel version - continuing without shadows"
        End If
        On Error GoTo 0
        
        ' Rounded corner adjustment with error handling
        On Error Resume Next
        .Adjustments(1) = 0.1  ' Subtle rounding
        On Error GoTo 0
    End With
    
    Debug.Print "Foundation created for: " & config.CardID
End Sub

' Layer 2: Creates the text layers (title and value) with compatibility enhancements
Private Sub CreateCardTextLayers(ws As Worksheet, config As StyledCardConfig)
    ' Create title text
    Call CreateTitleText(ws, config)
    
    ' Create value text  
    Call CreateValueText(ws, config)
    
    Debug.Print "Text layers created for: " & config.CardID
End Sub

' Creates the title text with enhanced positioning
Private Sub CreateTitleText(ws As Worksheet, config As StyledCardConfig)
    Dim titleShape As Shape
    
    ' Calculate title positioning
    Dim titleX As Double, titleY As Double, titleW As Double, titleH As Double
    titleX = config.XPosition + 10
    titleY = config.YPosition + 8
    titleW = config.Width - 20
    titleH = 30
    
    ' Create title textbox using numeric constant for compatibility
    Set titleShape = ws.Shapes.AddTextbox(MSO_TEXT_ORIENTATION_HORIZONTAL, titleX, titleY, titleW, titleH)
    titleShape.Name = config.CardID & "_Title"
    
    ' Configure title text with compatibility focus
    With titleShape.TextFrame
        .Characters.Text = config.TitleText
        .Characters.Font.Name = "Segoe UI"
        .Characters.Font.Size = config.TitleFontSize
        .Characters.Font.Color = config.TitleFontColor
        .Characters.Font.Bold = False
        .HorizontalAlignment = xlHAlignLeft
        .VerticalAlignment = xlVAlignTop
        .MarginLeft = 0
        .MarginRight = 0
        .MarginTop = 0
        .MarginBottom = 0
    End With
    
    ' Make textbox transparent
    With titleShape
        .Fill.Transparency = 1
        .Line.Transparency = 1
    End With
    
    Debug.Print "Title text created: " & config.TitleText
End Sub

' Creates the value text with formula support and enhanced compatibility
Private Sub CreateValueText(ws As Worksheet, config As StyledCardConfig)
    Dim valueShape As Shape
    
    ' Calculate value positioning (centered in lower portion)
    Dim valueX As Double, valueY As Double, valueW As Double, valueH As Double
    valueX = config.XPosition + 10
    valueY = config.YPosition + 35
    valueW = config.Width - 20
    valueH = config.Height - 45
    
    ' Create value textbox
    Set valueShape = ws.Shapes.AddTextbox(MSO_TEXT_ORIENTATION_HORIZONTAL, valueX, valueY, valueW, valueH)
    valueShape.Name = config.CardID & "_Value"
    
    ' Configure value text
    With valueShape.TextFrame
        ' Set the formula or static text
        If Left(config.ValueFormula, 1) = "=" Then
            ' It's a formula - use it directly
            .Characters.Text = config.ValueFormula
        Else
            ' It's static text
            .Characters.Text = config.ValueFormula
        End If
        
        .Characters.Font.Name = "Segoe UI"
        .Characters.Font.Size = config.ValueFontSize
        .Characters.Font.Color = config.ValueFontColor
        .Characters.Font.Bold = True
        .HorizontalAlignment = xlHAlignCenter
        .VerticalAlignment = xlVAlignCenter
        .MarginLeft = 0
        .MarginRight = 0
        .MarginTop = 0
        .MarginBottom = 0
    End With
    
    ' Make textbox transparent
    With valueShape
        .Fill.Transparency = 1
        .Line.Transparency = 1
    End With
    
    Debug.Print "Value text created with content: " & Left(config.ValueFormula, 50)
End Sub

'==================================================================================
'  UTILITY FUNCTIONS
'==================================================================================

' Deletes existing card components if they exist
Private Sub DeleteCardIfExists(ws As Worksheet, cardID As String)
    On Error Resume Next
    
    ' Delete base shape
    ws.Shapes(cardID & "_Base").Delete
    
    ' Delete title text
    ws.Shapes(cardID & "_Title").Delete
    
    ' Delete value text
    ws.Shapes(cardID & "_Value").Delete
    
    On Error GoTo 0
    Debug.Print "Cleaned up existing card: " & cardID
End Sub

' Creates a default card configuration with sensible defaults
Public Function CreateDefaultCardConfig(cardID As String, x As Double, y As Double, _
                                       titleText As String, valueFormula As String) As StyledCardConfig
    Dim config As StyledCardConfig
    
    ' Set position and size
    config.XPosition = x
    config.YPosition = y
    config.Width = DEFAULT_CARD_WIDTH
    config.Height = DEFAULT_CARD_HEIGHT
    
    ' Set content
    config.TitleText = titleText
    config.ValueFormula = valueFormula
    
    ' Set default styling
    config.CardFillColor = CARD_FILL_COLOR
    config.CardBorderColor = CARD_BORDER_COLOR
    config.TitleFontColor = TITLE_FONT_COLOR
    config.ValueFontColor = VALUE_FONT_COLOR
    config.TitleFontSize = DEFAULT_TITLE_FONT_SIZE
    config.ValueFontSize = DEFAULT_VALUE_FONT_SIZE
    
    ' Set identifier
    config.CardID = cardID
    
    CreateDefaultCardConfig = config
    Debug.Print "Default config created for: " & cardID
End Function

' Adds a dashboard title to the worksheet
Public Sub AddDashboardTitle(ws As Worksheet, titleText As String, Optional x As Double = 50, Optional y As Double = 20)
    Dim titleShape As Shape
    
    ' Delete existing title if present
    On Error Resume Next
    ws.Shapes("DashboardTitle").Delete
    On Error GoTo 0
    
    ' Create title textbox
    Set titleShape = ws.Shapes.AddTextbox(MSO_TEXT_ORIENTATION_HORIZONTAL, x, y, 600, 40)
    titleShape.Name = "DashboardTitle"
    
    ' Configure title
    With titleShape.TextFrame
        .Characters.Text = titleText
        .Characters.Font.Name = "Segoe UI"
        .Characters.Font.Size = 20
        .Characters.Font.Color = RGB(255, 255, 255)
        .Characters.Font.Bold = True
        .HorizontalAlignment = xlHAlignLeft
        .VerticalAlignment = xlVAlignCenter
    End With
    
    ' Make transparent
    With titleShape
        .Fill.Transparency = 1
        .Line.Transparency = 1
    End With
    
    Debug.Print "Dashboard title added: " & titleText
End Sub

'==================================================================================
'  DATA SETUP UTILITIES
'==================================================================================

' Sets up sample financial data for testing and examples
Public Sub SetupSampleFinancialData()
    Dim dataWs As Worksheet
    
    ' Create or get the FinData worksheet
    Set dataWs = CreateOrGetWorksheet("FinData")
    
    ' Clear existing data
    dataWs.Cells.Clear
    
    ' Add headers
    dataWs.Range("A1").Value = "Category"
    dataWs.Range("B1").Value = "Revenue"
    dataWs.Range("C1").Value = "Expenses"
    
    ' Add sample data
    dataWs.Range("A2").Value = "Q1 Sales"
    dataWs.Range("B2").Value = 150000
    dataWs.Range("C2").Value = 85000
    
    dataWs.Range("A3").Value = "Q2 Sales"
    dataWs.Range("B3").Value = 175000
    dataWs.Range("C3").Value = 92000
    
    dataWs.Range("A4").Value = "Q3 Sales"
    dataWs.Range("B4").Value = 185000
    dataWs.Range("C4").Value = 95000
    
    dataWs.Range("A5").Value = "Q4 Sales"
    dataWs.Range("B5").Value = 195000
    dataWs.Range("C5").Value = 98000
    
    dataWs.Range("A6").Value = "Marketing"
    dataWs.Range("B6").Value = 0
    dataWs.Range("C6").Value = 45000
    
    dataWs.Range("A7").Value = "Operations"
    dataWs.Range("B7").Value = 0
    dataWs.Range("C7").Value = 35000
    
    dataWs.Range("A8").Value = "Admin"
    dataWs.Range("B8").Value = 0
    dataWs.Range("C8").Value = 25000
    
    Debug.Print "Sample financial data setup complete"
End Sub
