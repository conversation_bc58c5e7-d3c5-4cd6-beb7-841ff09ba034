
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
'~          ULTRA SIMPLE TEST - MAXIMUM COMPATIBILITY
'~
'~ Description: Basic test that works on ALL Excel versions without any MSO constants
'~ Version: V1.0 (Ultra Compatible)
'~ Author: MiniMax Agent
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Option Explicit

' Ultra simple test - no complex constants needed
Public Sub UltraSimpleTest()
    On Error GoTo TestError
    
    Debug.Print "=== ULTRA SIMPLE COMPATIBILITY TEST ==="
    Debug.Print "Starting at: " & Now()
    
    ' Test 1: Basic worksheet operations
    Debug.Print "Test 1: Worksheet operations..."
    Dim testWs As Worksheet
    Set testWs = CreateOrGetWorksheet("UltraTest")
    testWs.Range("A1").Value = "COMPATIBILITY TEST"
    testWs.Range("A2").Value = "SUCCESS!"
    Debug.Print "✓ Worksheet operations work"
    
    ' Test 2: Basic shape creation (using simple rectangle)
    Debug.Print "Test 2: Basic shape creation..."
    Call CreateSimpleTestCard(testWs)
    Debug.Print "✓ Shape creation works"
    
    ' Test 3: Colors and formatting
    Debug.Print "Test 3: Colors and formatting..."
    testWs.Range("A1:A2").Interior.Color = RGB(68, 84, 96)
    testWs.Range("A1:A2").Font.Color = RGB(255, 255, 255)
    testWs.Range("A1:A2").Font.Bold = True
    Debug.Print "✓ Colors and formatting work"
    
    Debug.Print "=== ULTRA SIMPLE TEST PASSED! ✓ ==="
    MsgBox "Ultra Simple Test PASSED! ✓" & vbCrLf & "Your Excel can run the dashboard system.", vbInformation, "Compatibility Test"
    Exit Sub
    
TestError:
    Debug.Print "ERROR in UltraSimpleTest: " & Err.Description
    MsgBox "Ultra Simple Test FAILED: " & Err.Description, vbCritical, "Compatibility Error"
End Sub

' Create a very basic test card without any complex MSO constants
Private Sub CreateSimpleTestCard(ws As Worksheet)
    Dim cardShape As Shape
    
    ' Create basic rectangle (1 = rectangle, works everywhere)
    Set cardShape = ws.Shapes.AddShape(1, 100, 100, 180, 120)
    
    ' Basic formatting that works in all Excel versions
    With cardShape
        .Name = "UltraTestCard"
        .Fill.ForeColor.RGB = RGB(68, 84, 96)   ' Dark blue-grey
        .Line.ForeColor.RGB = RGB(85, 85, 85)   ' Grey border
        .Line.Weight = 2
    End With
    
    ' Add simple text
    Dim textShape As Shape
    Set textShape = ws.Shapes.AddTextbox(0, 110, 130, 160, 60)  ' 0 = horizontal orientation
    
    With textShape
        .Name = "UltraTestText"
        .TextFrame.Characters.Text = "ULTRA SIMPLE" & vbCrLf & "TEST CARD" & vbCrLf & "✓ SUCCESS"
        .TextFrame.Characters.Font.Size = 12
        .TextFrame.Characters.Font.Bold = True
        .TextFrame.Characters.Font.Color = RGB(255, 255, 255)
        .Fill.Transparency = 1  ' Transparent background
        .Line.Transparency = 1  ' No border
    End With
End Sub

' Test just the core functions without any examples
Public Sub TestCoreOnly()
    Debug.Print "=== TESTING CORE FUNCTIONS ONLY ==="
    
    ' Test CreateOrGetWorksheet
    Dim testWs As Worksheet
    Set testWs = CreateOrGetWorksheet("CoreTest")
    Debug.Print "✓ CreateOrGetWorksheet works"
    
    ' Test PrepareCanvas
    Call PrepareCanvas(testWs)
    Debug.Print "✓ PrepareCanvas works"
    
    ' Test CreateDefaultCardConfig
    Dim config As StyledCardConfig
    config = CreateDefaultCardConfig("TestCard", 50, 50, "Test Title", "Test Value")
    Debug.Print "✓ CreateDefaultCardConfig works"
    
    ' Test CreateStyledCard
    Call CreateStyledCard(testWs, config)
    Debug.Print "✓ CreateStyledCard works"
    
    Debug.Print "=== CORE FUNCTIONS TEST PASSED! ==="
    MsgBox "Core Functions Test PASSED! ✓", vbInformation, "Core Test"
End Sub
