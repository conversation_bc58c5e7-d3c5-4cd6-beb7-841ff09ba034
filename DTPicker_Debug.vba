' DTPicker Debugging Module - Runtime Error 1004 Diagnostics
' This module will help isolate the exact cause of your DTPicker error
' All output will be displayed in the Immediate window (Ctrl+G in VBA Editor)

Option Explicit

' Main debugging routine - Run this first
Public Sub DebugDTPickerIssue()
    Debug.Print "========================================="
    Debug.Print "DTPicker Diagnostic Report - " & Now()
    Debug.Print "========================================="
    
    ' Step 1: Check Excel version and architecture
    Call CheckExcelVersion
    
    ' Step 2: Check available references
    Call CheckVBAReferences
    
    ' Step 3: Check for DTPicker controls
    Call CheckDTPickerControls
    
    ' Step 4: Test basic object creation
    Call TestObjectCreation
    
    ' Step 5: Check worksheets and forms
    Call CheckWorksheetsAndForms
    
    Debug.Print "========================================="
    Debug.Print "Diagnostic Complete - Check results above"
    Debug.Print "========================================="
    
    MsgBox "Debugging complete! Check the Immediate window (Ctrl+G) for detailed results.", vbInformation
End Sub

' Check Excel version and architecture
Private Sub CheckExcelVersion()
    Debug.Print ""
    Debug.Print "--- EXCEL VERSION CHECK ---"
    Debug.Print "Excel Version: " & Application.Version
    Debug.Print "Excel Build: " & Application.Build
    
    ' Check if 64-bit (common cause of DTPicker issues)
    #If Win64 Then
        Debug.Print "Architecture: 64-bit (WARNING: DTPicker may not work properly)"
    #Else
        Debug.Print "Architecture: 32-bit (Should support DTPicker)"
    #End If
    
    Debug.Print "Operating System: " & Application.OperatingSystem
End Sub

' Check VBA references for Date/Time Picker
Private Sub CheckVBAReferences()
    Debug.Print ""
    Debug.Print "--- VBA REFERENCES CHECK ---"
    
    Dim ref As Object
    Dim foundDTPicker As Boolean
    foundDTPicker = False
    
    On Error Resume Next
    For Each ref In ThisWorkbook.VBProject.References
        If InStr(ref.Description, "Date") > 0 Or InStr(ref.Description, "Time") > 0 Or InStr(ref.Description, "MSCOMCT2") > 0 Then
            Debug.Print "Found Date/Time Reference: " & ref.Description
            Debug.Print "  - GUID: " & ref.GUID
            Debug.Print "  - Version: " & ref.Major & "." & ref.Minor
            Debug.Print "  - Path: " & ref.FullPath
            foundDTPicker = True
        End If
    Next ref
    On Error GoTo 0
    
    If Not foundDTPicker Then
        Debug.Print "WARNING: No Date/Time Picker references found!"
        Debug.Print "SOLUTION: Add reference to 'Microsoft Date and Time Picker Control 6.0'"
    End If
End Sub

' Check for existing DTPicker controls
Private Sub CheckDTPickerControls()
    Debug.Print ""
    Debug.Print "--- DTPICKER CONTROLS CHECK ---"
    
    Dim ws As Worksheet
    Dim shp As Shape
    Dim foundControls As Integer
    foundControls = 0
    
    ' Check each worksheet for DTPicker controls
    For Each ws In ThisWorkbook.Worksheets
        On Error Resume Next
        For Each shp In ws.Shapes
            If InStr(shp.Name, "DTPicker") > 0 Or shp.Type = msoOLEControlObject Then
                If InStr(shp.OLEFormat.progID, "MSCOMCT2") > 0 Then
                    Debug.Print "Found DTPicker on sheet '" & ws.Name & "': " & shp.Name
                    Debug.Print "  - ProgID: " & shp.OLEFormat.progID
                    foundControls = foundControls + 1
                End If
            End If
        Next shp
        On Error GoTo 0
    Next ws
    
    Debug.Print "Total DTPicker controls found: " & foundControls
    
    If foundControls = 0 Then
        Debug.Print "No DTPicker controls found on worksheets"
    End If
End Sub

' Test basic object creation
Private Sub TestObjectCreation()
    Debug.Print ""
    Debug.Print "--- OBJECT CREATION TEST ---"
    
    ' Test 1: Try to create DTPicker object
    On Error Resume Next
    Dim dtPicker As Object
    Set dtPicker = CreateObject("MSComCtl2.DTPicker")
    
    If Err.Number <> 0 Then
        Debug.Print "ERROR: Cannot create DTPicker object"
        Debug.Print "Error Number: " & Err.Number
        Debug.Print "Error Description: " & Err.Description
        Debug.Print "CAUSE: DTPicker control not properly registered or not available"
    Else
        Debug.Print "SUCCESS: DTPicker object created successfully"
        Set dtPicker = Nothing
    End If
    
    Err.Clear
    On Error GoTo 0
    
    ' Test 2: Try alternative date controls
    On Error Resume Next
    Dim monthView As Object
    Set monthView = CreateObject("MSComCtl2.MonthView")
    
    If Err.Number <> 0 Then
        Debug.Print "MonthView control also not available"
    Else
        Debug.Print "Alternative: MonthView control is available"
        Set monthView = Nothing
    End If
    
    Err.Clear
    On Error GoTo 0
End Sub

' Check worksheets and UserForms
Private Sub CheckWorksheetsAndForms()
    Debug.Print ""
    Debug.Print "--- WORKSHEETS AND FORMS CHECK ---"
    
    ' List all worksheets
    Debug.Print "Worksheets in workbook:"
    Dim ws As Worksheet
    For Each ws In ThisWorkbook.Worksheets
        Debug.Print "  - " & ws.Name
    Next ws
    
    ' Check for UserForms (if any)
    On Error Resume Next
    Dim vbComp As Object
    Dim formCount As Integer
    formCount = 0
    
    For Each vbComp In ThisWorkbook.VBProject.VBComponents
        If vbComp.Type = 3 Then ' vbext_ct_MSForm
            Debug.Print "UserForm found: " & vbComp.Name
            formCount = formCount + 1
        End If
    Next vbComp
    
    If formCount = 0 Then
        Debug.Print "No UserForms found in this workbook"
    End If
    
    On Error GoTo 0
End Sub

' Test specific DTPicker code that's causing the error
Public Sub TestSpecificDTPickerCode()
    Debug.Print ""
    Debug.Print "--- TESTING SPECIFIC DTPICKER CODE ---"
    
    ' This simulates common DTPicker usage patterns that cause errors
    On Error GoTo DTPickerError
    
    ' Test 1: Direct reference (most common error source)
    Debug.Print "Test 1: Attempting direct DTPicker reference..."
    ' Dim dtp As DTPicker  ' This line would cause compile error if reference missing
    
    ' Test 2: With statement (your specific error)
    Debug.Print "Test 2: Attempting With DTPicker statement..."
    ' With DTPicker1  ' This would cause Runtime Error 1004 if control doesn't exist
    '     .Value = Date
    ' End With
    
    Debug.Print "Cannot test actual DTPicker code without the control present"
    Debug.Print "Please paste your specific DTPicker code below for targeted debugging"
    
    Exit Sub
    
DTPickerError:
    Debug.Print "ERROR in DTPicker test:"
    Debug.Print "Error Number: " & Err.Number
    Debug.Print "Error Description: " & Err.Description
    Debug.Print "Error Source: " & Err.Source
End Sub

' Quick fix suggestions based on findings
Public Sub ShowFixSuggestions()
    Debug.Print ""
    Debug.Print "--- RECOMMENDED FIXES ---"
    Debug.Print "1. If 64-bit Excel: Replace DTPicker with cell-based date input"
    Debug.Print "2. If missing reference: Tools > References > Microsoft Date and Time Picker Control 6.0"
    Debug.Print "3. If control missing: Add DTPicker control to your form/sheet first"
    Debug.Print "4. Alternative: Use InputBox with date validation (see date_picker_fix.vba)"
    Debug.Print ""
    Debug.Print "Run DebugDTPickerIssue() first to identify the specific cause"
End Sub
