
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
'~          SIMPLE TEST MODULE - COMPATIBILITY FOCUSED
'~
'~ Description: Basic test functions that work across all Excel versions
'~ Version: V1.2 (Compatibility Enhanced)
'~ Author: MiniMax Agent
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Option Explicit

' Simple test without complex MSO constants
Public Sub SimpleTest_Basic()
    On Error GoTo TestError
    
    Debug.Print "=== SIMPLE COMPATIBILITY TEST ==="
    Debug.Print "Time: " & Now()
    
    ' Test 1: Create worksheet
    Debug.Print "Step 1: Creating test worksheet..."
    Dim ws As Worksheet
    Set ws = CreateOrGetWorksheet("SimpleTest")
    Debug.Print "✓ Worksheet created successfully"
    
    ' Test 2: Prepare simple canvas
    Debug.Print "Step 2: Preparing canvas..."
    Call PrepareSimpleCanvas(ws)
    Debug.Print "✓ Canvas prepared successfully"
    
    ' Test 3: Create basic shape (no shadows)
    Debug.Print "Step 3: Creating basic test shape..."
    Call CreateBasicTestShape(ws)
    Debug.Print "✓ Basic shape created successfully"
    
    Debug.Print "=== SIMPLE TEST COMPLETED SUCCESSFULLY! ==="
    MsgBox "Simple test passed! ✓", vbInformation, "Test Result"
    Exit Sub
    
TestError:
    Debug.Print "ERROR in SimpleTest_Basic: " & Err.Description
    MsgBox "Simple test failed: " & Err.Description, vbCritical, "Test Error"
End Sub

' Simplified canvas preparation
Private Sub PrepareSimpleCanvas(ws As Worksheet)
    ' Just set background color without complex properties
    With ws.Cells.Interior
        .Color = 3355443  ' Deep slate blue
    End With
    
    ' Hide gridlines if possible
    On Error Resume Next
    Application.ActiveWindow.DisplayGridlines = False
    Application.ActiveWindow.DisplayHeadings = False
    On Error GoTo 0
End Sub

' Create a basic test shape without complex MSO constants
Private Sub CreateBasicTestShape(ws As Worksheet)
    Dim testShape As Shape
    
    ' Create simple rectangle (use numeric constant)
    Set testShape = ws.Shapes.AddShape(1, 100, 100, 200, 100)  ' 1 = rectangle
    
    ' Basic formatting that works everywhere
    With testShape
        .Name = "TestShape_Basic"
        .Fill.ForeColor.RGB = RGB(68, 84, 96)  ' Dark grey-blue
        .Line.ForeColor.RGB = RGB(85, 85, 85)  ' Grey border
        .Line.Weight = 1
    End With
    
    ' Add simple text box
    Dim textShape As Shape
    Set textShape = ws.Shapes.AddTextbox(0, 110, 110, 180, 80)  ' 0 = horizontal
    
    With textShape
        .Name = "TestText_Basic"
        .TextFrame.Characters.Text = "COMPATIBILITY TEST" & vbCrLf & "SUCCESS!"
        .TextFrame.Characters.Font.Color = RGB(255, 255, 255)
        .TextFrame.Characters.Font.Size = 12
        .TextFrame.Characters.Font.Bold = True
        .Fill.Transparency = 1  ' Transparent background
        .Line.Transparency = 1  ' No border
    End With
End Sub

' Run all simple tests
Public Sub RunSimpleTests()
    Debug.Print "=== STARTING SIMPLE COMPATIBILITY TESTS ==="
    Call SimpleTest_Basic
    Debug.Print "=== ALL SIMPLE TESTS COMPLETED ==="
End Sub
